import os
import csv
from datetime import datetime, time
from collections import defaultdict

def get_time_period(start_time):
    """根据起始时间判断时段"""
    t = start_time.time()

    if time(6, 0) <= t < time(7, 30):
        return "06:00-07:30"
    elif time(7, 30) <= t < time(9, 0):
        return "07:30-09:00"
    elif time(9, 0) <= t < time(10, 30):
        return "09:00-10:30"
    elif time(10, 30) <= t < time(12, 0):
        return "10:30-12:00"
    elif time(12, 0) <= t < time(13, 30):
        return "12:00-13:30"
    elif time(13, 30) <= t < time(15, 0):
        return "13:30-15:00"
    elif time(15, 0) <= t < time(16, 30):
        return "15:00-16:30"
    elif time(16, 30) <= t < time(18, 0):
        return "16:30-18:00"
    elif time(18, 0) <= t < time(19, 30):
        return "18:00-19:30"
    elif time(19, 30) <= t < time(21, 0):
        return "19:30-21:00"
    return None

def find_max_stop_time_sources():
    """找到最长停留时间记录的具体来源"""
    
    # 目标交叉口和时段
    targets = [
        ("大学城东路-大学城南路交叉口", "07:30-09:00", 270.0),  # 第一长
        ("大学城东路-大学城南二路交叉口", "07:30-09:00", 229.0),  # 第二长
        ("大学城东路-思贤路交叉口", "15:00-16:30", 220.0)  # 第三长
    ]
    
    intersections_dir = '按交叉口分组的数据'
    
    print("=== 追溯最长停留时间记录的具体来源 ===\n")
    
    for target_intersection, target_period, target_time in targets:
        print(f"🔍 正在追溯: {target_intersection} - {target_period} - {target_time}秒")
        
        intersection_path = os.path.join(intersections_dir, target_intersection)
        if not os.path.exists(intersection_path):
            print(f"   ❌ 找不到交叉口目录: {target_intersection}")
            continue
        
        vehicle_files = [f for f in os.listdir(intersection_path) if f.endswith('.txt')]
        found = False
        
        for vehicle_file in vehicle_files:
            vehicle_path = os.path.join(intersection_path, vehicle_file)
            car_num = vehicle_file.replace('CarNum_', '').replace('.txt', '')
            
            # 读取车辆数据
            data = []
            with open(vehicle_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)  # 跳过标题行
                for row in reader:
                    if len(row) < 5:
                        continue
                    try:
                        gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                        speed = float(row[4])
                        data.append((gps_time, speed))
                    except ValueError:
                        continue
            
            if not data:
                continue
                
            # 按时间排序
            data.sort(key=lambda x: x[0])
            
            # 按日期分组分析
            daily_data = defaultdict(list)
            for gps_time, speed in data:
                date_key = gps_time.date()
                daily_data[date_key].append((gps_time, speed))
            
            # 分析每天的数据
            for date_key, day_data in daily_data.items():
                # 按时段分析该车该天的通过情况
                time_period_passages = defaultdict(list)
                
                for gps_time, speed in day_data:
                    time_period = get_time_period(gps_time)
                    if time_period:
                        time_period_passages[time_period].append((gps_time, speed))
                
                # 分析目标时段的停车情况
                if target_period in time_period_passages:
                    passage_data = time_period_passages[target_period]
                    
                    i = 0
                    while i < len(passage_data):
                        gps_time, speed = passage_data[i]
                        
                        # 检查是否停车（速度<=5km/h）
                        if speed <= 5.0:
                            # 找到停车事件的开始和结束
                            stop_start_time = gps_time
                            stop_end_time = gps_time
                            j = i
                            
                            # 向后查找停车结束时间
                            while j < len(passage_data) and passage_data[j][1] <= 5.0:
                                stop_end_time = passage_data[j][0]
                                j += 1
                            
                            # 计算停车时间
                            stop_duration = (stop_end_time - stop_start_time).total_seconds()
                            
                            # 检查是否是目标停留时间
                            if abs(stop_duration - target_time) < 1.0:  # 允许1秒误差
                                print(f"   ✅ 找到匹配记录!")
                                print(f"      📁 文件: {vehicle_file}")
                                print(f"      🚌 车辆编号: {car_num}")
                                print(f"      📅 日期: {date_key}")
                                print(f"      ⏰ 停车开始时间: {stop_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                                print(f"      ⏰ 停车结束时间: {stop_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                                print(f"      ⏱️  停留时长: {stop_duration:.1f}秒 ({stop_duration/60:.2f}分钟)")
                                print(f"      🎯 时段: {target_period}")
                                found = True
                                break
                            
                            i = j  # 跳过已处理的停车时间段
                        else:
                            i += 1
                
                if found:
                    break
            
            if found:
                break
        
        if not found:
            print(f"   ❌ 未找到匹配的{target_time}秒停留记录")
        
        print()

if __name__ == '__main__':
    find_max_stop_time_sources()
