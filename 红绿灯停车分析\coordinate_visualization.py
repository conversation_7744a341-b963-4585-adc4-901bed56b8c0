#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
停止线坐标可视化程序
读取Excel文件中的经纬度坐标，并在地图上进行可视化展示
"""

import pandas as pd
import folium
from folium import plugins
import webbrowser
import os

def load_coordinates(excel_file):
    """
    从Excel文件中加载坐标数据
    
    Args:
        excel_file (str): Excel文件路径
        
    Returns:
        pandas.DataFrame: 包含坐标数据的DataFrame
    """
    try:
        df = pd.read_excel(excel_file)
        print(f"成功加载数据，共{len(df)}个坐标点")
        print(f"数据列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"加载Excel文件时出错: {e}")
        return None

def create_map(df, output_file='coordinate_map.html'):
    """
    创建包含坐标点的交互式地图
    
    Args:
        df (pandas.DataFrame): 坐标数据
        output_file (str): 输出HTML文件名
        
    Returns:
        folium.Map: 创建的地图对象
    """
    if df is None or df.empty:
        print("数据为空，无法创建地图")
        return None
    
    # 计算地图中心点（所有坐标的平均值）
    center_lat = df['纬度'].mean()
    center_lon = df['经度'].mean()
    
    print(f"地图中心点: 纬度 {center_lat:.6f}, 经度 {center_lon:.6f}")
    
    # 创建地图对象
    m = folium.Map(
        location=[center_lat, center_lon],
        zoom_start=13,
        tiles='OpenStreetMap'
    )
    
    # 为不同方向设置不同颜色
    direction_colors = {
        '下行': 'red',
        '上行': 'blue',
        '其他': 'green'
    }
    
    # 添加坐标点到地图
    for idx, row in df.iterrows():
        lat = row['纬度']
        lon = row['经度']
        direction = row['方向'] if '方向' in df.columns else '其他'
        name = row['停止线名称'] if '停止线名称' in df.columns else f'点{idx+1}'
        
        # 选择颜色
        color = direction_colors.get(direction, 'green')
        
        # 创建弹出窗口内容
        popup_text = f"""
        <b>停止线名称:</b> {name}<br>
        <b>方向:</b> {direction}<br>
        <b>经度:</b> {lon:.6f}<br>
        <b>纬度:</b> {lat:.6f}<br>
        <b>序号:</b> {idx+1}
        """
        
        # 添加标记点
        folium.Marker(
            location=[lat, lon],
            popup=folium.Popup(popup_text, max_width=300),
            tooltip=f"{name} ({direction})",
            icon=folium.Icon(color=color, icon='info-sign')
        ).add_to(m)
    
    # 添加图例
    legend_html = '''
    <div style="position: fixed; 
                bottom: 50px; left: 50px; width: 150px; height: 90px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; padding: 10px">
    <p><b>图例</b></p>
    <p><i class="fa fa-map-marker fa-2x" style="color:red"></i> 下行</p>
    <p><i class="fa fa-map-marker fa-2x" style="color:blue"></i> 上行</p>
    <p><i class="fa fa-map-marker fa-2x" style="color:green"></i> 其他</p>
    </div>
    '''
    m.get_root().html.add_child(folium.Element(legend_html))
    
    # 添加全屏插件
    plugins.Fullscreen().add_to(m)
    
    # 添加测量工具
    plugins.MeasureControl().add_to(m)
    
    # 保存地图
    m.save(output_file)
    print(f"地图已保存为: {output_file}")
    
    return m

def create_cluster_map(df, output_file='coordinate_cluster_map.html'):
    """
    创建带聚类功能的地图
    
    Args:
        df (pandas.DataFrame): 坐标数据
        output_file (str): 输出HTML文件名
    """
    if df is None or df.empty:
        print("数据为空，无法创建聚类地图")
        return None
    
    # 计算地图中心点
    center_lat = df['纬度'].mean()
    center_lon = df['经度'].mean()
    
    # 创建地图对象
    m = folium.Map(
        location=[center_lat, center_lon],
        zoom_start=13,
        tiles='OpenStreetMap'
    )
    
    # 创建标记聚类
    marker_cluster = plugins.MarkerCluster().add_to(m)
    
    # 添加坐标点到聚类
    for idx, row in df.iterrows():
        lat = row['纬度']
        lon = row['经度']
        direction = row['方向'] if '方向' in df.columns else '其他'
        name = row['停止线名称'] if '停止线名称' in df.columns else f'点{idx+1}'
        
        popup_text = f"""
        <b>停止线名称:</b> {name}<br>
        <b>方向:</b> {direction}<br>
        <b>经度:</b> {lon:.6f}<br>
        <b>纬度:</b> {lat:.6f}<br>
        <b>序号:</b> {idx+1}
        """
        
        folium.Marker(
            location=[lat, lon],
            popup=folium.Popup(popup_text, max_width=300),
            tooltip=f"{name} ({direction})"
        ).add_to(marker_cluster)
    
    # 保存聚类地图
    m.save(output_file)
    print(f"聚类地图已保存为: {output_file}")
    
    return m

def main():
    """主函数"""
    excel_file = '停止线点坐标.xlsx'
    
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: 找不到文件 {excel_file}")
        return
    
    # 加载数据
    print("正在加载坐标数据...")
    df = load_coordinates(excel_file)
    
    if df is not None:
        # 显示数据基本信息
        print("\n数据概览:")
        print(df.head())
        print(f"\n坐标范围:")
        print(f"经度范围: {df['经度'].min():.6f} ~ {df['经度'].max():.6f}")
        print(f"纬度范围: {df['纬度'].min():.6f} ~ {df['纬度'].max():.6f}")
        
        # 创建普通地图
        print("\n正在创建地图...")
        map_obj = create_map(df, 'coordinate_map.html')
        
        # 创建聚类地图
        print("正在创建聚类地图...")
        cluster_map = create_cluster_map(df, 'coordinate_cluster_map.html')
        
        if map_obj is not None:
            print("\n地图创建成功！")
            print("生成的文件:")
            print("1. coordinate_map.html - 普通标记地图")
            print("2. coordinate_cluster_map.html - 聚类标记地图")
            
            # 询问是否打开地图
            try:
                choice = input("\n是否在浏览器中打开地图？(y/n): ").lower().strip()
                if choice in ['y', 'yes', '是']:
                    webbrowser.open('coordinate_map.html')
                    print("地图已在浏览器中打开")
            except:
                print("无法获取用户输入，请手动打开 coordinate_map.html 文件")

if __name__ == "__main__":
    main()
