import os
import csv
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.font_manager as fm
from datetime import datetime, time
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体"""
    chinese_fonts = [
        'Microsoft YaHei',
        'SimHei', 
        'SimSun',
        'KaiTi',
        'FangSong',
        'STSong',
        'STKaiti',
        'STFangsong'
    ]
    
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font]
        plt.rcParams['axes.unicode_minus'] = False
        print(f"使用字体: {selected_font}")
        return True
    else:
        print("警告：未找到合适的中文字体，将使用英文标签")
        return False

def get_time_period(start_time):
    """根据起始时间判断时段"""
    t = start_time.time()

    if time(6, 0) <= t < time(7, 30):
        return "06:00-07:30"
    elif time(7, 30) <= t < time(9, 0):
        return "07:30-09:00"
    elif time(9, 0) <= t < time(10, 30):
        return "09:00-10:30"
    elif time(10, 30) <= t < time(12, 0):
        return "10:30-12:00"
    elif time(12, 0) <= t < time(13, 30):
        return "12:00-13:30"
    elif time(13, 30) <= t < time(15, 0):
        return "13:30-15:00"
    elif time(15, 0) <= t < time(16, 30):
        return "15:00-16:30"
    elif time(16, 30) <= t < time(18, 0):
        return "16:30-18:00"
    elif time(18, 0) <= t < time(19, 30):
        return "18:00-19:30"
    elif time(19, 30) <= t < time(21, 0):
        return "19:30-21:00"
    return None

def analyze_max_stop_time_for_intersection(intersection_folder):
    """分析单个交叉口的最长停留时间"""
    intersection_name = os.path.basename(intersection_folder)
    print(f"正在分析交叉口: {intersection_name}")
    
    # 存储每个时段的所有停留时间
    time_period_stop_times = defaultdict(list)
    
    # 获取该交叉口下的所有车辆文件
    vehicle_files = [f for f in os.listdir(intersection_folder) if f.endswith('.txt')]
    
    for vehicle_file in vehicle_files:
        vehicle_path = os.path.join(intersection_folder, vehicle_file)
        car_num = vehicle_file.replace('CarNum_', '').replace('.txt', '')
        
        # 读取车辆数据
        data = []
        with open(vehicle_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过标题行
            for row in reader:
                if len(row) < 5:
                    continue
                try:
                    gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                    speed = float(row[4])
                    data.append((gps_time, speed))
                except ValueError:
                    continue
        
        if not data:
            continue
            
        # 按时间排序
        data.sort(key=lambda x: x[0])
        
        # 按日期分组分析
        daily_data = defaultdict(list)
        for gps_time, speed in data:
            date_key = gps_time.date()
            daily_data[date_key].append((gps_time, speed))
        
        # 分析每天的数据
        for date_key, day_data in daily_data.items():
            # 按时段分析该车该天的通过情况
            time_period_passages = defaultdict(list)
            
            for gps_time, speed in day_data:
                time_period = get_time_period(gps_time)
                if time_period:
                    time_period_passages[time_period].append((gps_time, speed))
            
            # 分析每个时段的停车情况
            for time_period, passage_data in time_period_passages.items():
                if not passage_data:
                    continue
                
                # 分析这次通过的停车情况
                i = 0
                while i < len(passage_data):
                    gps_time, speed = passage_data[i]
                    
                    # 检查是否停车（速度<=5km/h）
                    if speed <= 5.0:
                        # 找到停车事件的开始和结束
                        stop_start_time = gps_time
                        stop_end_time = gps_time
                        j = i
                        
                        # 向后查找停车结束时间
                        while j < len(passage_data) and passage_data[j][1] <= 5.0:
                            stop_end_time = passage_data[j][0]
                            j += 1
                        
                        # 计算停车时间
                        stop_duration = (stop_end_time - stop_start_time).total_seconds()
                        
                        # 如果停车时间超过1秒，记录停车时间
                        if stop_duration >= 1:
                            time_period_stop_times[time_period].append(stop_duration)
                        
                        i = j  # 跳过已处理的停车时间段
                    else:
                        i += 1
    
    # 计算每个时段的最长停留时间
    max_stop_times = {}
    for time_period, stop_times in time_period_stop_times.items():
        if stop_times:
            max_stop_times[time_period] = max(stop_times)
        else:
            max_stop_times[time_period] = 0.0
    
    return intersection_name, max_stop_times

def extract_intersection_number(intersection_name):
    """从交叉口名称中提取前面的数字编号"""
    import re
    # 使用正则表达式提取开头的数字
    match = re.match(r'^(\d+)', intersection_name)
    if match:
        return int(match.group(1))
    else:
        # 如果没有找到数字，返回一个很大的数，让它排在最后
        return 999


def create_max_stop_time_heatmap():
    """创建最长停留时间热力图"""
    # 按交叉口分组的数据目录
    intersections_dir = '按交叉口分组的数据_下行'
    
    if not os.path.exists(intersections_dir):
        print(f"错误：找不到目录 {intersections_dir}")
        return
    
    # 获取所有交叉口文件夹
    intersection_folders = [f for f in os.listdir(intersections_dir) 
                           if os.path.isdir(os.path.join(intersections_dir, f))]
    
    print(f"找到 {len(intersection_folders)} 个交叉口")
    
    # 存储所有分析结果
    all_results = {}
    summary_data = []
    
    # 时段顺序
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00", 
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00", 
                         "18:00-19:30", "19:30-21:00"]
    
    # 分析每个交叉口
    for intersection_folder in intersection_folders:
        intersection_path = os.path.join(intersections_dir, intersection_folder)
        intersection_name, max_stop_times = analyze_max_stop_time_for_intersection(intersection_path)
        all_results[intersection_name] = max_stop_times
        
        # 生成汇总数据
        for time_period in time_periods_order:
            max_time = max_stop_times.get(time_period, 0.0)
            summary_data.append({
                '交叉口': intersection_name,
                '时段': time_period,
                '最长停留时间(秒)': round(max_time, 2),
                '最长停留时间(分钟)': round(max_time / 60, 3)
            })
    
    # 保存结果到CSV
    df_summary = pd.DataFrame(summary_data)
    df_summary = df_summary.sort_values(['交叉口', '时段'])
    df_summary.to_csv('各交叉口最长停留时间分析结果.csv', index=False, encoding='utf_8_sig')
    print(f"\n分析结果已保存到: 各交叉口最长停留时间分析结果.csv")
    
    # 设置中文字体
    has_chinese_font = setup_chinese_font()
    
    # 创建热力图
    plt.figure(figsize=(16, 12))
    pivot_max_time = df_summary.pivot(index='交叉口', columns='时段', values='最长停留时间(分钟)')
    pivot_max_time = pivot_max_time.reindex(columns=time_periods_order)

    # 按交叉口名称前的数字编号排序
    intersection_names = list(pivot_max_time.index)
    intersection_names_sorted = sorted(intersection_names, key=extract_intersection_number)
    print(f"交叉口排序前: {intersection_names}")
    print(f"交叉口排序后: {intersection_names_sorted}")
    pivot_max_time = pivot_max_time.reindex(index=intersection_names_sorted)
    
    if has_chinese_font:
        # 使用中文标签
        sns.heatmap(pivot_max_time, annot=True, fmt='.2f', cmap='Reds', 
                    cbar_kws={'label': '最长停留时间 (分钟)'}, 
                    linewidths=0.5, square=False)
        plt.title('各交叉口分时段最长停留时间热力图', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('时段', fontsize=12)
        plt.ylabel('交叉口', fontsize=12)
    else:
        # 使用英文标签
        sns.heatmap(pivot_max_time, annot=True, fmt='.2f', cmap='Reds', 
                    cbar_kws={'label': 'Maximum Stop Time (minutes)'}, 
                    linewidths=0.5, square=False)
        plt.title('Maximum Stop Time Heatmap by Time Period and Intersection', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('Time Period', fontsize=12)
        plt.ylabel('Intersection', fontsize=12)
    
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('最长停留时间热力图.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计摘要
    print("\n=== 各交叉口最长停留时间分析摘要 ===")
    print("\n各时段最长停留时间最高的交叉口:")
    for time_period in time_periods_order:
        period_data = df_summary[df_summary['时段'] == time_period]
        if not period_data.empty:
            max_record = period_data.loc[period_data['最长停留时间(分钟)'].idxmax()]
            print(f"  {time_period}: {max_record['交叉口']} ({max_record['最长停留时间(分钟)']:.2f}分钟)")
    
    print("\n整体最长停留时间最高的交叉口:")
    intersection_max_times = df_summary.groupby('交叉口')['最长停留时间(分钟)'].max().sort_values(ascending=False)
    for i, (intersection, max_time) in enumerate(intersection_max_times.head(10).items(), 1):
        print(f"  {i:2d}. {intersection}: {max_time:.2f}分钟")
    
    print(f"\n最长停留时间热力图已生成并保存为: 最长停留时间热力图.png")

if __name__ == '__main__':
    create_max_stop_time_heatmap()
