import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from shapely.geometry import Point, LineString, Polygon
from shapely.ops import transform, split
import pyproj
from geopy.distance import geodesic
import folium
from folium import plugins
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CurvedRouteSpeedAnalyzer:
    def __init__(self, start_point, middle_point, end_point, data_folder):
        """
        初始化拐弯路段速度分布分析器
        
        Args:
            start_point: 起点坐标 (lat, lon)
            middle_point: 中间点坐标 (lat, lon) - 拐弯点
            end_point: 终点坐标 (lat, lon)
            data_folder: 数据文件夹路径
        """
        self.start_point = start_point
        self.middle_point = middle_point
        self.end_point = end_point
        self.data_folder = data_folder
        
        # 存储分析结果
        self.filtered_data = []
        self.route_line = None
        self.route_polygon = None
        self.segments = []
        
        print(f"初始化拐弯路段分析器:")
        print(f"起点: {start_point}")
        print(f"中间点: {middle_point}")
        print(f"终点: {end_point}")
        print(f"数据文件夹: {data_folder}")

    def calculate_distance(self, point1, point2):
        """
        计算两点间距离（米）
        """
        return geodesic(point1, point2).meters

    def point_to_line_distance(self, point, line_start, line_end):
        """
        计算点到线段的距离（米）
        """
        # 转换为投影坐标系进行精确计算
        transformer = pyproj.Transformer.from_crs('EPSG:4326', 'EPSG:3857', always_xy=True)
        
        point_proj = transformer.transform(point[1], point[0])
        line_start_proj = transformer.transform(line_start[1], line_start[0])
        line_end_proj = transformer.transform(line_end[1], line_end[0])
        
        line = LineString([line_start_proj, line_end_proj])
        point_geom = Point(point_proj)
        
        return line.distance(point_geom)

    def filter_buses_through_curved_route(self, radius=20):
        """
        步骤1: 识别经过三个必经点的公交车数据（适用于拐弯路段）
        """
        print("正在筛选经过拐弯路段三个必经点的公交车数据...")
        
        # 创建输出文件夹
        output_folder = "filtered_curved_route_data"
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        
        filtered_files = []
        
        for filename in os.listdir(self.data_folder):
            if filename.endswith('.txt') and filename.startswith('CarNum_'):
                filepath = os.path.join(self.data_folder, filename)
                
                try:
                    # 读取数据
                    df = pd.read_csv(filepath)
                    
                    # 检查是否经过三个必经点
                    passed_start = False
                    passed_middle = False
                    passed_end = False
                    
                    for _, row in df.iterrows():
                        point = (row['Latitude'], row['Longitude'])
                        
                        # 检查是否在起点范围内
                        if self.calculate_distance(point, self.start_point) <= radius:
                            passed_start = True
                        
                        # 检查是否在中间点范围内
                        if self.calculate_distance(point, self.middle_point) <= radius:
                            passed_middle = True
                        
                        # 检查是否在终点范围内
                        if self.calculate_distance(point, self.end_point) <= radius:
                            passed_end = True
                        
                        # 如果三个点都经过了，可以提前退出循环
                        if passed_start and passed_middle and passed_end:
                            break
                    
                    # 如果经过所有三个必经点，保存数据
                    if passed_start and passed_middle and passed_end:
                        output_path = os.path.join(output_folder, filename)
                        df.to_csv(output_path, index=False)
                        filtered_files.append(filename)
                        self.filtered_data.append(df)
                        print(f"✓ {filename} 经过拐弯路段三个必经点，已保存")
                    
                except Exception as e:
                    print(f"处理文件 {filename} 时出错: {e}")
        
        print(f"\n共找到 {len(filtered_files)} 辆公交车经过拐弯路段")
        return filtered_files

    def create_curved_route_corridor(self, width=15):
        """
        步骤2: 创建拐弯路线走廊多边形（由两段直线组成）
        """
        print("正在创建拐弯路线走廊...")
        
        # 创建两段路线：起点到中间点，中间点到终点
        segment1 = LineString([(self.start_point[1], self.start_point[0]), 
                              (self.middle_point[1], self.middle_point[0])])
        segment2 = LineString([(self.middle_point[1], self.middle_point[0]),
                              (self.end_point[1], self.end_point[0])])
        
        # 使用投影坐标系创建缓冲区
        transformer_to_proj = pyproj.Transformer.from_crs('EPSG:4326', 'EPSG:3857', always_xy=True)
        transformer_to_geo = pyproj.Transformer.from_crs('EPSG:3857', 'EPSG:4326', always_xy=True)
        
        # 转换为投影坐标并创建缓冲区
        segment1_proj = transform(transformer_to_proj.transform, segment1)
        segment2_proj = transform(transformer_to_proj.transform, segment2)
        
        buffer1_proj = segment1_proj.buffer(width)
        buffer2_proj = segment2_proj.buffer(width)
        
        # 合并两个缓冲区
        combined_buffer_proj = buffer1_proj.union(buffer2_proj)
        
        # 转换回地理坐标
        self.route_polygon = transform(transformer_to_geo.transform, combined_buffer_proj)
        
        # 同时保存完整的路线（用于分段）
        self.route_line = LineString([
            (self.start_point[1], self.start_point[0]),
            (self.middle_point[1], self.middle_point[0]),
            (self.end_point[1], self.end_point[0])
        ])
        
        print(f"拐弯路线走廊创建完成，宽度: {width*2}米")
        return self.route_polygon

    def create_curved_segments(self, segment_length=20):
        """
        步骤3: 将拐弯路线分段（确保middle点作为分段端点）
        """
        print(f"正在将拐弯路线分段，每段长度: {segment_length}米...")

        # 计算两段路线的长度
        segment1_length = geodesic(self.start_point, self.middle_point).meters
        segment2_length = geodesic(self.middle_point, self.end_point).meters
        total_length = segment1_length + segment2_length

        print(f"第一段长度: {segment1_length:.1f}米 (起点→中间点)")
        print(f"第二段长度: {segment2_length:.1f}米 (中间点→终点)")
        print(f"总长度: {total_length:.1f}米")

        self.segments = []
        segment_id = 0

        # 第一段：从起点到中间点
        current_distance = 0
        while current_distance < segment1_length:
            segment_start_distance = current_distance
            segment_end_distance = min(current_distance + segment_length, segment1_length)

            # 如果这一段会跨越middle点，则在middle点处截断
            if segment_end_distance > segment1_length:
                segment_end_distance = segment1_length

            start_coords = self.get_point_at_distance(segment_start_distance, segment1_length)
            end_coords = self.get_point_at_distance(segment_end_distance, segment1_length)

            segment_info = {
                'id': segment_id,
                'start': start_coords,
                'end': end_coords,
                'start_distance': segment_start_distance,
                'end_distance': segment_end_distance,
                'speeds': [],
                'zero_speed_points': [],
                'avg_speed': 0,
                'speed_count': 0,
                'zero_speed_count': 0,
                'section': 1  # 标记为第一段
            }

            self.segments.append(segment_info)
            current_distance = segment_end_distance
            segment_id += 1

        # 第二段：从中间点到终点
        current_distance = segment1_length  # 从middle点开始
        while current_distance < total_length:
            segment_start_distance = current_distance
            segment_end_distance = min(current_distance + segment_length, total_length)

            start_coords = self.get_point_at_distance(segment_start_distance, segment1_length)
            end_coords = self.get_point_at_distance(segment_end_distance, segment1_length)

            segment_info = {
                'id': segment_id,
                'start': start_coords,
                'end': end_coords,
                'start_distance': segment_start_distance,
                'end_distance': segment_end_distance,
                'speeds': [],
                'zero_speed_points': [],
                'avg_speed': 0,
                'speed_count': 0,
                'zero_speed_count': 0,
                'section': 2  # 标记为第二段
            }

            self.segments.append(segment_info)
            current_distance = segment_end_distance
            segment_id += 1

        print(f"路线分段完成，共 {len(self.segments)} 段")
        print(f"第一段（起点→中间点）: {sum(1 for s in self.segments if s['section'] == 1)} 段")
        print(f"第二段（中间点→终点）: {sum(1 for s in self.segments if s['section'] == 2)} 段")
        return self.segments

    def get_point_at_distance(self, distance, segment1_length):
        """
        根据距离获取路线上的点坐标
        """
        if distance <= segment1_length:
            # 在第一段（起点到中间点）
            ratio = distance / segment1_length
            lat = self.start_point[0] + (self.middle_point[0] - self.start_point[0]) * ratio
            lon = self.start_point[1] + (self.middle_point[1] - self.start_point[1]) * ratio
        else:
            # 在第二段（中间点到终点）
            segment2_distance = distance - segment1_length
            segment2_length = geodesic(self.middle_point, self.end_point).meters
            ratio = segment2_distance / segment2_length
            lat = self.middle_point[0] + (self.end_point[0] - self.middle_point[0]) * ratio
            lon = self.middle_point[1] + (self.end_point[1] - self.middle_point[1]) * ratio
        
        return (lat, lon)

    def calculate_segment_speeds(self, corridor_width=15):
        """
        步骤4: 计算各段的速度分布
        """
        print("正在计算各段速度分布...")

        total_points = 0
        processed_points = 0

        for i, df in enumerate(self.filtered_data):
            print(f"处理第 {i+1}/{len(self.filtered_data)} 个数据文件，共 {len(df)} 个数据点...")

            for _, row in df.iterrows():
                total_points += 1
                point = (row['Latitude'], row['Longitude'])
                speed = row['GpsSpeed']

                # 检查点是否在路线走廊内
                if self.is_point_in_corridor(point, corridor_width):
                    # 找到最近的段
                    segment_idx = self.find_nearest_segment(point)
                    if segment_idx is not None:
                        self.segments[segment_idx]['speeds'].append(speed)
                        processed_points += 1

                        # 记录零速度点
                        if speed == 0:
                            self.segments[segment_idx]['zero_speed_points'].append(point)

                # 每处理1000个点显示一次进度
                if total_points % 1000 == 0:
                    print(f"已处理 {total_points} 个点，其中 {processed_points} 个在走廊内...")

        # 计算每段的平均速度和统计信息
        for segment in self.segments:
            if segment['speeds']:
                segment['avg_speed'] = np.mean(segment['speeds'])
                segment['speed_count'] = len(segment['speeds'])
                segment['zero_speed_count'] = len(segment['zero_speed_points'])
            else:
                segment['avg_speed'] = 0
                segment['speed_count'] = 0
                segment['zero_speed_count'] = 0

        print(f"速度计算完成！总共处理 {total_points} 个点，其中 {processed_points} 个在走廊内")
        return self.segments

    def is_point_in_corridor(self, point, corridor_width):
        """
        检查点是否在拐弯路线走廊内（优化版本）
        """
        # 先做简单的边界框检查，快速排除明显不在范围内的点
        min_lat = min(self.start_point[0], self.middle_point[0], self.end_point[0]) - 0.001
        max_lat = max(self.start_point[0], self.middle_point[0], self.end_point[0]) + 0.001
        min_lon = min(self.start_point[1], self.middle_point[1], self.end_point[1]) - 0.001
        max_lon = max(self.start_point[1], self.middle_point[1], self.end_point[1]) + 0.001

        if not (min_lat <= point[0] <= max_lat and min_lon <= point[1] <= max_lon):
            return False

        # 检查点到第一段的距离
        dist1 = self.point_to_line_distance(point, self.start_point, self.middle_point)
        # 检查点到第二段的距离
        dist2 = self.point_to_line_distance(point, self.middle_point, self.end_point)

        # 如果到任一段的距离小于走廊宽度，则认为在走廊内
        return min(dist1, dist2) <= corridor_width

    def find_nearest_segment(self, point):
        """
        找到点最近的路段
        """
        min_distance = float('inf')
        nearest_segment_idx = None
        
        for i, segment in enumerate(self.segments):
            # 计算点到段中心的距离
            segment_center = (
                (segment['start'][0] + segment['end'][0]) / 2,
                (segment['start'][1] + segment['end'][1]) / 2
            )
            distance = self.calculate_distance(point, segment_center)
            
            if distance < min_distance:
                min_distance = distance
                nearest_segment_idx = i
        
        return nearest_segment_idx

    def create_interactive_map(self, output_folder, direction_name=""):
        """
        创建交互式地图
        """
        # 计算地图中心点
        center_lat = (self.start_point[0] + self.middle_point[0] + self.end_point[0]) / 3
        center_lon = (self.start_point[1] + self.middle_point[1] + self.end_point[1]) / 3

        m = folium.Map(
            location=[center_lat, center_lon],
            zoom_start=15,
            tiles='OpenStreetMap'
        )

        # 添加三个关键点标记
        folium.Marker(
            location=[self.start_point[0], self.start_point[1]],
            popup=f'{direction_name} - 起点',
            icon=folium.Icon(color='green', icon='play')
        ).add_to(m)

        folium.Marker(
            location=[self.middle_point[0], self.middle_point[1]],
            popup=f'{direction_name} - 中间点（拐弯点）',
            icon=folium.Icon(color='blue', icon='pause')
        ).add_to(m)

        folium.Marker(
            location=[self.end_point[0], self.end_point[1]],
            popup=f'{direction_name} - 终点',
            icon=folium.Icon(color='red', icon='stop')
        ).add_to(m)

        # 速度颜色映射函数
        def get_speed_color(speed_kmh):
            speed_ranges = [
                (0, 5, '#8B0000'),      # 深红色 0-5 km/h
                (5, 10, '#FF0000'),     # 红色 5-10 km/h
                (10, 15, '#FF4500'),    # 橙红色 10-15 km/h
                (15, 20, '#FFA500'),    # 橙色 15-20 km/h
                (20, 25, '#FFD700'),    # 金色 20-25 km/h
                (25, 30, '#ADFF2F'),    # 绿黄色 25-30 km/h
                (30, 35, '#32CD32'),    # 酸橙绿 30-35 km/h
                (35, 40, '#228B22'),    # 森林绿 35-40 km/h
                (40, 45, '#006400'),    # 深绿色 40-45 km/h
                (45, float('inf'), '#004000')  # 极深绿色 45+ km/h
            ]

            for min_speed, max_speed, color in speed_ranges:
                if min_speed <= speed_kmh < max_speed:
                    return color, f"{min_speed}-{max_speed if max_speed != float('inf') else '45+'} km/h"
            return '#808080', '未知'

        # 添加路段线条和零速度点
        for segment in self.segments:
            if segment['speed_count'] > 0:
                color, speed_range = get_speed_color(segment['avg_speed'])

                # 创建段的线
                folium.PolyLine(
                    locations=[
                        [segment['start'][0], segment['start'][1]],
                        [segment['end'][0], segment['end'][1]]
                    ],
                    color=color,
                    weight=8,
                    opacity=0.8,
                    popup=f"{direction_name} - 段 {segment['id']+1}: 平均速度 {segment['avg_speed']:.2f} km/h<br>"
                          f"速度范围: {speed_range}<br>"
                          f"总数据点: {segment['speed_count']} 个<br>"
                          f"零速度点: {segment['zero_speed_count']} 个"
                ).add_to(m)

                # 添加速度为0的点
                for zero_point in segment['zero_speed_points']:
                    folium.CircleMarker(
                        location=[zero_point[0], zero_point[1]],
                        radius=2,
                        color='black',
                        fillColor='red',
                        fillOpacity=0.3,
                        opacity=0.7,
                        popup=f"{direction_name} - 速度为0的点"
                    ).add_to(m)

        # 创建图例
        legend_items = []
        legend_items.append(f'<p><b>{direction_name} - 拐弯路段速度图例 (每5km/h一个颜色)</b></p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #8B0000"></i> 0-5 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FF0000"></i> 5-10 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FF4500"></i> 10-15 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FFA500"></i> 15-20 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FFD700"></i> 20-25 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #ADFF2F"></i> 25-30 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #32CD32"></i> 30-35 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #228B22"></i> 35-40 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #006400"></i> 40-45 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #004000"></i> 45+ km/h</p>')
        legend_items.append('<hr>')
        legend_items.append('<p><b>各段数据点数量:</b></p>')

        for segment in self.segments:
            if segment['speed_count'] > 0:
                legend_items.append(f'<p>段{segment["id"]+1}: {segment["speed_count"]}点 (零速度:{segment["zero_speed_count"]})</p>')

        legend_items.append('<hr>')
        legend_items.append('<p><i class="fa fa-circle" style="color: red; opacity: 0.3"></i> 速度为0的点 (70%透明度)</p>')

        legend_html = f'''
        <div style="position: fixed; bottom: 50px; left: 50px; z-index: 1000; background-color: white;
                    padding: 10px; border: 2px solid grey; border-radius: 5px; max-height: 400px; overflow-y: auto; font-size: 12px;">
            {"".join(legend_items)}
        </div>
        '''
        m.get_root().html.add_child(folium.Element(legend_html))

        # 保存地图
        map_path = os.path.join(output_folder, f"{direction_name}_curved_route_speed_map.html")
        m.save(map_path)
        print(f"{direction_name}拐弯路段交互式地图已保存至: {map_path}")

        return map_path

    def create_segment_chart(self, output_folder, direction_name=""):
        """
        创建各段速度条形图
        """
        plt.figure(figsize=(12, 6))

        segment_ids = [f"段{i+1}" for i in range(len(self.segments))]
        avg_speeds = [segment['avg_speed'] if segment['speed_count'] > 0 else 0 for segment in self.segments]

        # 使用颜色映射函数
        def get_speed_color(speed_kmh):
            if speed_kmh < 5:
                return '#8B0000'
            elif speed_kmh < 10:
                return '#FF0000'
            elif speed_kmh < 15:
                return '#FF4500'
            elif speed_kmh < 20:
                return '#FFA500'
            elif speed_kmh < 25:
                return '#FFD700'
            elif speed_kmh < 30:
                return '#ADFF2F'
            elif speed_kmh < 35:
                return '#32CD32'
            elif speed_kmh < 40:
                return '#228B22'
            elif speed_kmh < 45:
                return '#006400'
            else:
                return '#004000'

        colors = []
        for speed in avg_speeds:
            if speed > 0:
                colors.append(get_speed_color(speed))
            else:
                colors.append('gray')

        bars = plt.bar(segment_ids, avg_speeds, color=colors, alpha=0.8, edgecolor='black')
        plt.xlabel('路段', fontsize=12)
        plt.ylabel('平均速度 (km/h)', fontsize=12)
        plt.title(f'{direction_name} - 拐弯路段各段平均速度分布', fontsize=14, fontweight='bold')
        plt.xticks(rotation=45)
        plt.grid(axis='y', alpha=0.3)

        # 添加数据点数量标签
        for i, bar in enumerate(bars):
            count = self.segments[i]['speed_count']
            if count > 0:
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                         f"{count}点", ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        # 保存图表
        chart_path = os.path.join(output_folder, f"{direction_name}_curved_segment_speed_chart.png")
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"{direction_name}拐弯路段速度分布图表已保存至: {chart_path}")

        return chart_path

    def create_interval_chart(self, output_folder, direction_name=""):
        """
        创建速度区间分布柱状图
        """
        print(f"正在为{direction_name}创建速度区间分布图...")

        # 收集所有速度数据点
        all_speeds = []
        for segment in self.segments:
            all_speeds.extend(segment['speeds'])

        if not all_speeds:
            print(f"{direction_name}没有速度数据，跳过速度区间分布图")
            return None

        # 定义速度区间
        max_speed = max(all_speeds)
        intervals = []
        interval_counts = []
        interval_labels = []

        current_speed = 0
        while current_speed <= max_speed + 5:
            next_speed = current_speed + 5

            count = sum(1 for speed in all_speeds if current_speed <= speed < next_speed)

            intervals.append((current_speed, next_speed))
            interval_counts.append(count)

            if next_speed > max_speed + 5:
                interval_labels.append(f"{current_speed}+ km/h")
            else:
                interval_labels.append(f"{current_speed}-{next_speed} km/h")

            current_speed = next_speed

            if current_speed > 100:
                break

        # 创建柱状图
        plt.figure(figsize=(12, 8))

        colors = ['#8B0000', '#FF0000', '#FF4500', '#FFA500', '#FFD700',
                 '#ADFF2F', '#32CD32', '#228B22', '#006400', '#004000']

        while len(colors) < len(interval_counts):
            colors.append('#004000')

        bars = plt.bar(range(len(interval_counts)), interval_counts,
                      color=colors[:len(interval_counts)], alpha=0.8, edgecolor='black')

        plt.xlabel('速度区间 (km/h)', fontsize=12)
        plt.ylabel('数据点数量', fontsize=12)
        plt.title(f'{direction_name} - 拐弯路段速度区间分布统计\n(每5km/h一个区间)', fontsize=14, fontweight='bold')
        plt.xticks(range(len(interval_labels)), interval_labels, rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)

        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            if height > 0:
                plt.text(bar.get_x() + bar.get_width()/2, height + max(interval_counts)*0.01,
                        f'{int(height)}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图表
        interval_chart_path = os.path.join(output_folder, f"{direction_name}_curved_speed_interval_distribution.png")
        plt.savefig(interval_chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"{direction_name}拐弯路段速度区间分布图已保存至: {interval_chart_path}")

        # 输出统计信息
        print(f"\n{direction_name}拐弯路段速度区间统计:")
        total_points = sum(interval_counts)
        for i, (interval, count) in enumerate(zip(intervals, interval_counts)):
            if count > 0:
                percentage = (count / total_points) * 100
                print(f"{interval_labels[i]}: {count} 个点 ({percentage:.1f}%)")

        return interval_chart_path

    def export_detailed_data(self, output_folder, direction_name=""):
        """
        导出详细数据到CSV
        """
        results_df = pd.DataFrame({
            '段ID': [i+1 for i in range(len(self.segments))],
            '起点纬度': [segment['start'][0] for segment in self.segments],
            '起点经度': [segment['start'][1] for segment in self.segments],
            '终点纬度': [segment['end'][0] for segment in self.segments],
            '终点经度': [segment['end'][1] for segment in self.segments],
            '平均速度(km/h)': [segment['avg_speed'] if segment['speed_count'] > 0 else 0 for segment in self.segments],
            '数据点数量': [segment['speed_count'] for segment in self.segments],
            '零速度点数量': [segment['zero_speed_count'] for segment in self.segments]
        })

        csv_path = os.path.join(output_folder, f"{direction_name}_curved_route_speed_data.csv")
        results_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"{direction_name}拐弯路段分析数据已保存至: {csv_path}")

        return csv_path

    def run_complete_analysis(self, output_folder="curved_route_analysis_results", direction_name=""):
        """
        运行完整的拐弯路段分析
        """
        print(f"\n{'='*80}")
        print(f"开始拐弯路段速度分布分析...")
        print(f"{'='*80}")
        print(f"起点: {self.start_point}")
        print(f"中间点: {self.middle_point}")
        print(f"终点: {self.end_point}")
        print(f"数据文件夹: {self.data_folder}")
        print("-" * 50)

        # 创建输出文件夹
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        # 步骤1: 筛选经过拐弯路段的公交车
        filtered_files = self.filter_buses_through_curved_route(radius=20)

        if not filtered_files:
            print("未找到经过拐弯路段三个必经点的公交车数据！")
            return None

        # 步骤2: 创建拐弯路线走廊
        self.create_curved_route_corridor(width=15)

        # 步骤3: 分段并计算速度
        self.create_curved_segments(segment_length=20)
        self.calculate_segment_speeds(corridor_width=15)

        # 步骤4: 生成可视化结果
        print(f"\n正在为{direction_name}生成可视化结果...")

        # 1. 创建交互式地图
        map_path = self.create_interactive_map(output_folder, direction_name)

        # 2. 创建各段速度条形图
        chart_path = self.create_segment_chart(output_folder, direction_name)

        # 3. 创建速度区间分布图
        interval_chart_path = self.create_interval_chart(output_folder, direction_name)

        # 4. 导出详细数据CSV
        csv_path = self.export_detailed_data(output_folder, direction_name)

        # 输出分析摘要
        self.print_analysis_summary(direction_name)

        print(f"\n{'='*80}")
        print(f"{direction_name}拐弯路段分析完成！")
        print(f"{'='*80}")

        return {
            'map_path': map_path,
            'chart_path': chart_path,
            'interval_chart_path': interval_chart_path,
            'csv_path': csv_path,
            'filtered_files_count': len(filtered_files)
        }

    def print_analysis_summary(self, direction_name=""):
        """
        输出分析摘要
        """
        # 收集所有速度数据
        all_speeds = []
        total_points = 0
        zero_speed_points = 0

        for segment in self.segments:
            all_speeds.extend(segment['speeds'])
            total_points += segment['speed_count']
            zero_speed_points += segment['zero_speed_count']

        if total_points > 0:
            overall_avg = np.mean(all_speeds)
            zero_speed_percentage = (zero_speed_points / total_points) * 100

            print(f"\n{direction_name}拐弯路段分析摘要:")
            print(f"- 总数据点: {total_points:,}个")
            print(f"- 平均速度: {overall_avg:.2f} km/h")
            print(f"- 零速度点: {zero_speed_points:,}个 ({zero_speed_percentage:.1f}%)")
            print(f"- 有效路段数: {sum(1 for s in self.segments if s['speed_count'] > 0)}/{len(self.segments)}")

            # 找出最快和最慢的段
            valid_segments = [s for s in self.segments if s['speed_count'] > 0]
            if valid_segments:
                fastest_segment = max(valid_segments, key=lambda x: x['avg_speed'])
                slowest_segment = min(valid_segments, key=lambda x: x['avg_speed'])
                print(f"- 最快段: 段{fastest_segment['id']+1} ({fastest_segment['avg_speed']:.2f} km/h)")
                print(f"- 最慢段: 段{slowest_segment['id']+1} ({slowest_segment['avg_speed']:.2f} km/h)")
        else:
            print(f"\n{direction_name}没有有效的速度数据")


class CurvedRouteDualDirectionAnalyzer:
    """
    拐弯路段双向分析器
    """
    def __init__(self, start_point, middle_point, end_point, upward_folder, downward_folder):
        self.start_point = start_point
        self.middle_point = middle_point
        self.end_point = end_point
        self.upward_folder = upward_folder
        self.downward_folder = downward_folder

        self.upward_results = None
        self.downward_results = None

    def run_dual_direction_analysis(self):
        """
        运行双向拐弯路段分析
        """
        print("开始拐弯路段双向速度分布对比分析...")
        print(f"起点: {self.start_point}")
        print(f"中间点: {self.middle_point}")
        print(f"终点: {self.end_point}")
        print(f"上行数据文件夹: {self.upward_folder}")
        print(f"下行数据文件夹: {self.downward_folder}")

        # 创建输出文件夹
        output_base_folder = "curved_route_dual_analysis_results"
        if not os.path.exists(output_base_folder):
            os.makedirs(output_base_folder)

        # 分析上行数据
        print(f"\n{'='*60}")
        print(f"开始分析上行数据...")
        print(f"{'='*60}")

        upward_analyzer = CurvedRouteSpeedAnalyzer(
            self.start_point, self.middle_point, self.end_point, self.upward_folder
        )
        upward_output_folder = os.path.join(output_base_folder, "上行_results")
        self.upward_results = upward_analyzer.run_complete_analysis(upward_output_folder, "上行")

        # 分析下行数据
        print(f"\n{'='*60}")
        print(f"开始分析下行数据...")
        print(f"{'='*60}")

        downward_analyzer = CurvedRouteSpeedAnalyzer(
            self.start_point, self.middle_point, self.end_point, self.downward_folder
        )
        downward_output_folder = os.path.join(output_base_folder, "下行_results")
        self.downward_results = downward_analyzer.run_complete_analysis(downward_output_folder, "下行")

        # 生成对比报告
        if self.upward_results and self.downward_results:
            self.create_comparison_report(output_base_folder, upward_analyzer, downward_analyzer)

            print(f"\n{'='*80}")
            print("拐弯路段双向对比分析完成！")
            print(f"{'='*80}")

            print(f"\n生成的文件:")
            print(f"- 上行结果文件夹: {upward_output_folder}/")
            print(f"- 下行结果文件夹: {downward_output_folder}/")
            print(f"- 对比分析报告: {output_base_folder}/拐弯路段上下行对比分析报告.md")

            return True
        else:
            print("分析失败，请检查数据文件夹和参数设置。")
            return False

    def create_comparison_report(self, output_folder, upward_analyzer, downward_analyzer):
        """
        创建拐弯路段对比分析报告
        """
        # 收集统计数据
        upward_stats = self.collect_statistics(upward_analyzer, "上行")
        downward_stats = self.collect_statistics(downward_analyzer, "下行")

        # 生成报告内容
        report_content = self.generate_report_content(upward_stats, downward_stats)

        # 保存报告
        report_path = os.path.join(output_folder, "拐弯路段上下行对比分析报告.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"拐弯路段对比分析报告已保存至: {report_path}")
        return report_path

    def collect_statistics(self, analyzer, direction_name):
        """
        收集统计数据
        """
        # 收集所有速度数据
        all_speeds = []
        total_points = 0
        zero_speed_points = 0

        for segment in analyzer.segments:
            all_speeds.extend(segment['speeds'])
            total_points += segment['speed_count']
            zero_speed_points += segment['zero_speed_count']

        # 计算速度区间分布
        speed_intervals = {}
        interval_ranges = [(0, 5), (5, 10), (10, 15), (15, 20), (20, 25),
                          (25, 30), (30, 35), (35, 40), (40, 45), (45, float('inf'))]

        for min_speed, max_speed in interval_ranges:
            if max_speed == float('inf'):
                count = sum(1 for speed in all_speeds if speed >= min_speed)
                key = f"{min_speed}+ km/h"
            else:
                count = sum(1 for speed in all_speeds if min_speed <= speed < max_speed)
                key = f"{min_speed}-{max_speed} km/h"

            percentage = (count / total_points * 100) if total_points > 0 else 0
            speed_intervals[key] = {'count': count, 'percentage': percentage}

        # 找出最快和最慢的段
        valid_segments = [s for s in analyzer.segments if s['speed_count'] > 0]
        if valid_segments:
            fastest_segment = max(valid_segments, key=lambda x: x['avg_speed'])
            slowest_segment = min(valid_segments, key=lambda x: x['avg_speed'])
            avg_speeds = [s['avg_speed'] for s in valid_segments]
            speed_range = (min(avg_speeds), max(avg_speeds))
            overall_avg = np.mean(avg_speeds)
        else:
            fastest_segment = slowest_segment = None
            speed_range = (0, 0)
            overall_avg = 0

        return {
            'direction': direction_name,
            'total_points': total_points,
            'zero_speed_points': zero_speed_points,
            'zero_speed_percentage': (zero_speed_points / total_points * 100) if total_points > 0 else 0,
            'speed_intervals': speed_intervals,
            'speed_range': speed_range,
            'overall_avg': overall_avg,
            'fastest_segment': fastest_segment,
            'slowest_segment': slowest_segment,
            'valid_segments_count': len(valid_segments)
        }

    def generate_report_content(self, upward_stats, downward_stats):
        """
        生成拐弯路段对比报告内容
        """
        # 计算路段总长度
        segment1_length = geodesic(self.start_point, self.middle_point).meters
        segment2_length = geodesic(self.middle_point, self.end_point).meters
        total_length = segment1_length + segment2_length

        report = f"""# 拐弯路段上下行公交车速度分布对比分析报告

## 分析概述

本报告对比分析了拐弯路段上行和下行公交车的速度分布特征，该路段由两条直线组成：

### 路段信息
- **起点**: {self.start_point}
- **中间点（拐弯点）**: {self.middle_point}
- **终点**: {self.end_point}
- **第一段长度**: {segment1_length:.1f}米 (起点→中间点)
- **第二段长度**: {segment2_length:.1f}米 (中间点→终点)
- **总路段长度**: {total_length:.1f}米
- **分析方法**: 20米分段，共{upward_stats.get('valid_segments_count', 0) if upward_stats else 0}段

## 整体对比统计

| 指标 | 上行数据 | 下行数据 | 差异 |
|------|----------|----------|------|
| 总数据点 | {upward_stats['total_points']:,}个 | {downward_stats['total_points']:,}个 | 下行{'多' if downward_stats['total_points'] > upward_stats['total_points'] else '少'}{abs(downward_stats['total_points'] - upward_stats['total_points']) / upward_stats['total_points'] * 100:.1f}% |
| 零速度点 | {upward_stats['zero_speed_points']:,}个 ({upward_stats['zero_speed_percentage']:.1f}%) | {downward_stats['zero_speed_points']:,}个 ({downward_stats['zero_speed_percentage']:.1f}%) | {'上行' if upward_stats['zero_speed_percentage'] > downward_stats['zero_speed_percentage'] else '下行'}停车更频繁 |
| 平均速度范围 | {upward_stats['speed_range'][0]:.2f}-{upward_stats['speed_range'][1]:.2f} km/h | {downward_stats['speed_range'][0]:.2f}-{downward_stats['speed_range'][1]:.2f} km/h | 下行{'略高' if downward_stats['speed_range'][1] > upward_stats['speed_range'][1] else '略低'} |
| 整体平均速度 | {upward_stats['overall_avg']:.2f} km/h | {downward_stats['overall_avg']:.2f} km/h | 下行{'快' if downward_stats['overall_avg'] > upward_stats['overall_avg'] else '慢'}{abs(downward_stats['overall_avg'] - upward_stats['overall_avg']) / upward_stats['overall_avg'] * 100:.1f}% |

## 速度区间分布对比

| 速度区间 | 上行数据 | 下行数据 | 差异分析 |
|----------|----------|----------|----------|"""

        # 添加速度区间对比
        for interval in ['0-5 km/h', '5-10 km/h', '10-15 km/h', '15-20 km/h', '20-25 km/h',
                        '25-30 km/h', '30-35 km/h', '35-40 km/h', '40-45 km/h', '45+ km/h']:
            up_data = upward_stats['speed_intervals'].get(interval, {'count': 0, 'percentage': 0})
            down_data = downward_stats['speed_intervals'].get(interval, {'count': 0, 'percentage': 0})

            if up_data['percentage'] > down_data['percentage']:
                diff_analysis = "上行更多"
            elif down_data['percentage'] > up_data['percentage']:
                diff_analysis = "下行更多"
            else:
                diff_analysis = "基本相当"

            report += f"\n| {interval} | {up_data['count']:,} ({up_data['percentage']:.1f}%) | {down_data['count']:,} ({down_data['percentage']:.1f}%) | {diff_analysis} |"

        # 添加关键发现
        report += f"""

## 关键发现

### 1. 拐弯路段特殊性分析
- **路段特点**: 该路段包含一个拐弯点，由两条直线组成，可能在拐弯处出现减速现象
- **整体速度**: {'下行' if downward_stats['overall_avg'] > upward_stats['overall_avg'] else '上行'}整体更快，平均速度高{abs(downward_stats['overall_avg'] - upward_stats['overall_avg']):.2f} km/h
- **停车频率**: {'上行' if upward_stats['zero_speed_percentage'] > downward_stats['zero_speed_percentage'] else '下行'}停车更频繁，零速度点占比高{abs(upward_stats['zero_speed_percentage'] - downward_stats['zero_speed_percentage']):.1f}%

### 2. 拥堵点分析
- **上行最慢段**: 段{upward_stats['slowest_segment']['id']+1 if upward_stats['slowest_segment'] else 'N/A'} ({upward_stats['slowest_segment']['avg_speed']:.2f} km/h, {upward_stats['slowest_segment']['speed_count']}个数据点)
- **下行最慢段**: 段{downward_stats['slowest_segment']['id']+1 if downward_stats['slowest_segment'] else 'N/A'} ({downward_stats['slowest_segment']['avg_speed']:.2f} km/h, {downward_stats['slowest_segment']['speed_count']}个数据点)

### 3. 高速段分析
- **上行最快段**: 段{upward_stats['fastest_segment']['id']+1 if upward_stats['fastest_segment'] else 'N/A'} ({upward_stats['fastest_segment']['avg_speed']:.2f} km/h)
- **下行最快段**: 段{downward_stats['fastest_segment']['id']+1 if downward_stats['fastest_segment'] else 'N/A'} ({downward_stats['fastest_segment']['avg_speed']:.2f} km/h)

## 生成文件

### 上行数据文件
- 交互式地图: `{self.upward_results['map_path'] if self.upward_results else 'N/A'}`
- 各段速度图: `{self.upward_results['chart_path'] if self.upward_results else 'N/A'}`
- 速度区间分布图: `{self.upward_results['interval_chart_path'] if self.upward_results else 'N/A'}`
- 详细数据: `{self.upward_results['csv_path'] if self.upward_results else 'N/A'}`

### 下行数据文件
- 交互式地图: `{self.downward_results['map_path'] if self.downward_results else 'N/A'}`
- 各段速度图: `{self.downward_results['chart_path'] if self.downward_results else 'N/A'}`
- 速度区间分布图: `{self.downward_results['interval_chart_path'] if self.downward_results else 'N/A'}`
- 详细数据: `{self.downward_results['csv_path'] if self.downward_results else 'N/A'}`

## 拐弯路段特殊建议

1. **拐弯点优化**: 重点关注中间拐弯点附近的速度变化，考虑优化拐弯半径或信号配时
2. **分段分析**: 建议分别分析起点→中间点和中间点→终点两段的速度特征
3. **安全考虑**: 拐弯路段应特别关注安全性，适当的减速是正常现象
4. **实地调研**: 对拐弯点进行实地调研，了解几何设计对车辆行驶的影响

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report


def main():
    """
    主函数 - 执行拐弯路段双向速度分布对比分析
    """
    # 定义三个必经点坐标
    start_point = (29.577157, 106.308888)   # 起点 (纬度, 经度)
    middle_point = (29.577987, 106.309757)  # 中间点（拐弯点）
    end_point = (29.578556, 106.309268)     # 终点

    # 数据文件夹路径
    upward_folder = "上行数据"
    downward_folder = "下行数据"

    # 创建拐弯路段双向分析器实例
    analyzer = CurvedRouteDualDirectionAnalyzer(
        start_point, middle_point, end_point, upward_folder, downward_folder
    )

    # 运行完整分析
    success = analyzer.run_dual_direction_analysis()

    if success:
        print("\n✅ 拐弯路段双向对比分析成功完成！")
        print("请查看生成的文件以获取详细结果。")
    else:
        print("\n❌ 分析失败，请检查数据和参数设置。")


if __name__ == "__main__":
    main()
