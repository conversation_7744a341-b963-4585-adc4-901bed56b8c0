{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6e007e19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据加载和预处理完成。\n", "线路被划分为 2514 个约200米的区间\n", "每个区间的平均速度计算完成。\n", "segment\n", "0     1.380952\n", "2    22.000000\n", "3    19.285714\n", "5    23.600000\n", "6     9.888889\n", "Name: GpsSpeed, dtype: float64\n", "新地图已保存至 bus_route_speed_map_fixed_segments.html\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from math import radians, sin, cos, sqrt, atan2\n", "import folium\n", "from shapely.geometry import Point, LineString\n", "\n", "# 加载数据\n", "df = pd.read_csv('evening_peak.csv')\n", "\n", "# 剔除异常点\n", "Q1 = df[['Longitude', 'Latitude']].quantile(0.25)\n", "Q3 = df[['Longitude', 'Latitude']].quantile(0.75)\n", "IQR = Q3 - Q1\n", "df_no_outliers = df[~((df[['Longitude', 'Latitude']] < (Q1 - 1.5 * IQR)) |(df[['Longitude', 'Latitude']] > (Q3 + 1.5 * IQR))).any(axis=1)].copy()\n", "\n", "# 重置索引\n", "df_no_outliers.reset_index(drop=True, inplace=True)\n", "\n", "print(\"数据加载和预处理完成。\")\n", "\n", "# 辅助函数：计算两点之间的距离（Haversine公式）\n", "def haversine(lon1, lat1, lon2, lat2):\n", "    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])\n", "    dlon = lon2 - lon1\n", "    dlat = lat2 - lat1\n", "    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2\n", "    c = 2 * atan2(sqrt(a), sqrt(1-a))\n", "    return c * 6371000 # 地球半径（米）\n", "\n", "# 1. 构建公交线路\n", "# 将所有GPS点连接成一条线路\n", "route_coords = list(zip(df_no_outliers['Longitude'], df_no_outliers['Latitude']))\n", "route_line = LineString(route_coords)\n", "\n", "# 2. 固定距离分段\n", "segment_length = 200  # 200米\n", "segments = []\n", "current_dist = 0\n", "\n", "# 使用shapely的interpolate方法来创建等距点\n", "num_segments = int(route_line.length / (segment_length / 111000)) # 粗略估计\n", "\n", "points = [route_line.interpolate(i / num_segments, normalized=True) for i in range(num_segments + 1)]\n", "\n", "for i in range(len(points) - 1):\n", "    segment_start_point = points[i]\n", "    segment_end_point = points[i+1]\n", "    \n", "    # 为了处理折返，我们只考虑一个方向的线段\n", "    line_segment = LineString([segment_start_point, segment_end_point])\n", "    segments.append(line_segment)\n", "\n", "print(f\"线路被划分为 {len(segments)} 个约200米的区间\")\n", "\n", "# 3. 计算每个区间的速度平均值\n", "\n", "df_no_outliers['segment'] = -1\n", "\n", "# 为每个GPS点分配一个路段\n", "for i, point in df_no_outliers.iterrows():\n", "    gps_point = Point(point['Longitude'], point['Latitude'])\n", "    min_dist = float('inf')\n", "    assigned_segment = -1\n", "    for j, segment in enumerate(segments):\n", "        dist = gps_point.distance(segment)\n", "        if dist < min_dist:\n", "            min_dist = dist\n", "            assigned_segment = j\n", "    df_no_outliers.loc[i, 'segment'] = assigned_segment\n", "\n", "# 计算每个路段的平均速度\n", "segment_avg_speeds = df_no_outliers.groupby('segment')['GpsSpeed'].mean()\n", "\n", "print(\"每个区间的平均速度计算完成。\")\n", "print(segment_avg_speeds.head())\n", "\n", "# 4. 使用folium在地图上可视化\n", "# 计算地图中心\n", "center_lon = df_no_outliers['Longitude'].mean()\n", "center_lat = df_no_outliers['Latitude'].mean()\n", "\n", "m = folium.Map(location=[center_lat, center_lon], zoom_start=13)\n", "\n", "# 颜色映射\n", "def speed_to_color(speed):\n", "    if speed == 0:\n", "        return 'purple'\n", "    elif 0 < speed <= 10:\n", "        return 'red'\n", "    elif 10 < speed <= 20:\n", "        return 'orange'\n", "    elif 20 < speed <= 30:\n", "        return 'blue'\n", "    else: # speed > 30\n", "        return 'green'\n", "\n", "# 绘制路段\n", "for i, segment in enumerate(segments):\n", "    if i in segment_avg_speeds.index:\n", "        avg_speed = segment_avg_speeds[i]\n", "        color = speed_to_color(avg_speed)\n", "        points = [[p[1], p[0]] for p in segment.coords] # folium需要 (lat, lon)\n", "        folium.PolyLine(points, color=color, weight=5, opacity=0.8, popup=f'Avg Speed: {avg_speed:.2f} km/h').add_to(m)\n", "\n", "# 添加图例\n", "legend_html = '''\n", "<div style=\"position: fixed; \n", "     bottom: 50px; left: 50px; width: 220px; height: 150px; \n", "     border:2px solid grey; z-index:9999; font-size:14px;\n", "     \">&nbsp;<b>速度图例 (km/h)</b><br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:red\"></i>&nbsp;0 &lt; 速度 &le; 10<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:orange\"></i>&nbsp;10 &lt; 速度 &le; 20<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:blue\"></i>&nbsp;20 &lt; 速度 &le; 30<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:green\"></i>&nbsp;速度 &gt; 30<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:purple\"></i>&nbsp;速度 = 0\n", "</div>\n", "'''\n", "m.get_root().html.add_child(folium.Element(legend_html))\n", "\n", "# 保存地图\n", "m.save('bus_route_speed_map_fixed_segments.html')\n", "\n", "print(\"新地图已保存至 bus_route_speed_map_fixed_segments.html\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}