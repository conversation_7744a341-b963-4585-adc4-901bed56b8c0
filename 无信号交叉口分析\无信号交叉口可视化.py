import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 定义更好看的颜色映射
stop_prob_cmap = LinearSegmentedColormap.from_list('StopProb', ['#f7fcfd', '#e0ecf4', '#bfd3e6', '#9ebcda', '#8c96c6', '#8c6bb1', '#88419d', '#6e016b'])
transit_time_cmap = LinearSegmentedColormap.from_list('TransitTime', ['#ffffd9', '#edf8b1', '#c7e9b4', '#7fcdbb', '#41b6c4', '#1d91c0', '#225ea8', '#0c2c84'])

def load_data():
    """加载分析结果数据"""
    csv_path = '无信号交叉口停留分析结果_下行.csv'
    if not os.path.exists(csv_path):
        print(f"错误：找不到文件 {csv_path}")
        return None
    
    df = pd.read_csv(csv_path, encoding='utf_8_sig')
    print(f"成功加载数据，共 {len(df)} 条记录")
    return df

def create_enhanced_heatmaps(df):
    """创建增强版热力图"""
    # 确保时段顺序
    time_periods_order = ["早高峰(6:30-9:00)", "平峰(10:00-15:00)", "晚高峰(16:30-19:00)"]
    
    # 创建一个大图，包含两个热力图
    plt.figure(figsize=(18, 10))
    gs = gridspec.GridSpec(1, 2, width_ratios=[1, 1])
    
    # 1. 停留概率热力图
    ax1 = plt.subplot(gs[0])
    pivot_stop_prob = df.pivot(index='交叉口', columns='时段', values='停留概率(%)')
    pivot_stop_prob = pivot_stop_prob.reindex(columns=time_periods_order)
    
    sns.heatmap(pivot_stop_prob, annot=True, fmt='.1f', cmap=stop_prob_cmap,
                cbar_kws={'label': '停留概率 (%)'}, ax=ax1)
    ax1.set_title('无信号交叉口停留概率热力图', fontsize=16, fontweight='bold')
    ax1.set_xlabel('时段', fontsize=12)
    ax1.set_ylabel('交叉口', fontsize=12)
    ax1.set_xticklabels(ax1.get_xticklabels(), rotation=30, ha='right')
    
    # 2. 平均通过时间热力图
    ax2 = plt.subplot(gs[1])
    pivot_transit_time = df.pivot(index='交叉口', columns='时段', values='平均通过时间(分钟)')
    pivot_transit_time = pivot_transit_time.reindex(columns=time_periods_order)
    
    sns.heatmap(pivot_transit_time, annot=True, fmt='.2f', cmap=transit_time_cmap,
                cbar_kws={'label': '平均通过时间 (分钟)'}, ax=ax2)
    ax2.set_title('无信号交叉口平均通过时间热力图', fontsize=16, fontweight='bold')
    ax2.set_xlabel('时段', fontsize=12)
    ax2.set_ylabel('', fontsize=12)  # 不显示y轴标签，因为与左图相同
    ax2.set_xticklabels(ax2.get_xticklabels(), rotation=30, ha='right')
    
    plt.tight_layout()
    plt.savefig('无信号交叉口分析\无信号交叉口热力图_下行.png', dpi=300, bbox_inches='tight')
    print("热力图已保存到：无信号交叉口分析\无信号交叉口热力图_下行.png")
    plt.show()

def create_bar_charts(df):
    """创建柱状图比较不同交叉口和时段"""
    # 设置图表风格
    plt.style.use('ggplot')
    
    # 确保时段顺序
    time_periods_order = ["早高峰(6:30-9:00)", "平峰(10:00-15:00)", "晚高峰(16:30-19:00)"]
    
    # 1. 停留概率柱状图
    plt.figure(figsize=(14, 8))
    
    # 准备数据
    intersections = df['交叉口'].unique()
    x = np.arange(len(intersections))
    width = 0.25
    
    # 绘制每个时段的柱状图
    for i, period in enumerate(time_periods_order):
        period_data = df[df['时段'] == period]
        stop_probs = [period_data[period_data['交叉口'] == intersection]['停留概率(%)'].values[0] 
                     for intersection in intersections]
        
        plt.bar(x + i*width - width, stop_probs, width, 
                label=period, alpha=0.8)
    
    plt.xlabel('交叉口', fontsize=12)
    plt.ylabel('停留概率 (%)', fontsize=12)
    plt.title('各交叉口不同时段停留概率对比', fontsize=16, fontweight='bold')
    plt.xticks(x, intersections, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for i, period in enumerate(time_periods_order):
        period_data = df[df['时段'] == period]
        stop_probs = [period_data[period_data['交叉口'] == intersection]['停留概率(%)'].values[0] 
                     for intersection in intersections]
        
        for j, v in enumerate(stop_probs):
            plt.text(j + i*width - width, v + 2, f'{v:.1f}%', 
                    ha='center', va='bottom', fontsize=9, rotation=0)
    
    plt.tight_layout()
    plt.savefig('无信号交叉口分析\停留概率柱状图_下行.png', dpi=300, bbox_inches='tight')
    print("停留概率柱状图已保存到：无信号交叉口分析\停留概率柱状图_下行.png")
    plt.show()
    
    # 2. 平均通过时间柱状图
    plt.figure(figsize=(14, 8))
    
    # 绘制每个时段的柱状图
    for i, period in enumerate(time_periods_order):
        period_data = df[df['时段'] == period]
        transit_times = [period_data[period_data['交叉口'] == intersection]['平均通过时间(分钟)'].values[0] 
                        for intersection in intersections]
        
        plt.bar(x + i*width - width, transit_times, width, 
                label=period, alpha=0.8)
    
    plt.xlabel('交叉口', fontsize=12)
    plt.ylabel('平均通过时间 (分钟)', fontsize=12)
    plt.title('各交叉口不同时段平均通过时间对比', fontsize=16, fontweight='bold')
    plt.xticks(x, intersections, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for i, period in enumerate(time_periods_order):
        period_data = df[df['时段'] == period]
        transit_times = [period_data[period_data['交叉口'] == intersection]['平均通过时间(分钟)'].values[0] 
                        for intersection in intersections]
        
        for j, v in enumerate(transit_times):
            plt.text(j + i*width - width, v + 2, f'{v:.1f}', 
                    ha='center', va='bottom', fontsize=9, rotation=0)
    
    plt.tight_layout()
    plt.savefig('无信号交叉口分析\平均通过时间柱状图_下行.png', dpi=300, bbox_inches='tight')
    print("平均通过时间柱状图已保存到：无信号交叉口分析\平均通过时间柱状图_下行.png")
    plt.show()

def create_radar_chart(df):
    """创建雷达图比较不同交叉口的综合表现"""
    # 准备数据
    intersections = df['交叉口'].unique()
    time_periods = ["早高峰(6:30-9:00)", "平峰(10:00-15:00)", "晚高峰(16:30-19:00)"]
    
    # 计算每个交叉口在不同时段的平均停留概率
    avg_stop_probs = {}
    for intersection in intersections:
        intersection_data = df[df['交叉口'] == intersection]
        avg_stop_probs[intersection] = [
            intersection_data[intersection_data['时段'] == period]['停留概率(%)'].values[0]
            for period in time_periods
        ]
    
    # 创建雷达图
    plt.figure(figsize=(10, 8))
    
    # 设置雷达图的角度
    angles = np.linspace(0, 2*np.pi, len(time_periods), endpoint=False).tolist()
    angles += angles[:1]  # 闭合雷达图
    
    # 设置雷达图的轴
    ax = plt.subplot(111, polar=True)
    plt.xticks(angles[:-1], time_periods, fontsize=12)
    
    # 绘制每个交叉口的雷达图
    for i, intersection in enumerate(intersections):
        values = avg_stop_probs[intersection]
        values += values[:1]  # 闭合雷达图
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=intersection)
        ax.fill(angles, values, alpha=0.1)
    
    # 设置雷达图的刻度
    ax.set_rlabel_position(0)
    plt.yticks([20, 40, 60, 80], ["20%", "40%", "60%", "80%"], fontsize=10)
    plt.ylim(0, 100)
    
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    plt.title('各交叉口不同时段停留概率雷达图', fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('无信号交叉口分析\停留概率雷达图_下行.png', dpi=300, bbox_inches='tight')
    print("停留概率雷达图已保存到：无信号交叉口分析\停留概率雷达图_下行.png")
    plt.show()

def create_time_comparison(df):
    """创建时段对比图"""
    # 准备数据
    time_periods = ["早高峰(6:30-9:00)", "平峰(10:00-15:00)", "晚高峰(16:30-19:00)"]
    
    # 计算每个时段的平均停留概率和平均通过时间
    period_stats = {}
    for period in time_periods:
        period_data = df[df['时段'] == period]
        avg_stop_prob = period_data['停留概率(%)'].mean()
        avg_transit_time = period_data['平均通过时间(分钟)'].mean()
        period_stats[period] = (avg_stop_prob, avg_transit_time)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 1. 平均停留概率
    periods = list(period_stats.keys())
    stop_probs = [period_stats[period][0] for period in periods]
    
    bars1 = ax1.bar(periods, stop_probs, color='skyblue', alpha=0.8)
    ax1.set_xlabel('时段', fontsize=12)
    ax1.set_ylabel('平均停留概率 (%)', fontsize=12)
    ax1.set_title('不同时段平均停留概率对比', fontsize=14, fontweight='bold')
    ax1.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
    
    # 2. 平均通过时间
    transit_times = [period_stats[period][1] for period in periods]
    
    bars2 = ax2.bar(periods, transit_times, color='salmon', alpha=0.8)
    ax2.set_xlabel('时段', fontsize=12)
    ax2.set_ylabel('平均通过时间 (分钟)', fontsize=12)
    ax2.set_title('不同时段平均通过时间对比', fontsize=14, fontweight='bold')
    ax2.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数值标签
    for bar in bars2:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}', ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('无信号交叉口分析\时段对比图_下行.png', dpi=300, bbox_inches='tight')
    print("时段对比图已保存到：无信号交叉口分析\时段对比图_下行.png")
    plt.show()

def main():
    """主函数"""
    print("开始生成无信号交叉口分析可视化...")
    
    # 加载数据
    df = load_data()
    if df is None:
        return
    
    # 创建各种可视化
    create_enhanced_heatmaps(df)
    create_bar_charts(df)
    create_radar_chart(df)
    create_time_comparison(df)
    
    print("所有可视化图表已生成完毕！")

if __name__ == '__main__':
    main()
