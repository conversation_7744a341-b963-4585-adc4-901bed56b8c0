# 公交车速度分布分析报告（优化版）

## 分析概述

本次分析针对指定路段（起点：29.585958, 106.314104；终点：29.590679, 106.314488）的公交车速度分布进行了详细研究，采用了优化的可视化方案和更精细的分段策略。

## 分析参数

- **起点坐标**: (29.585958, 106.314104)
- **终点坐标**: (29.590679, 106.314488)
- **路段总长度**: 524.6 米
- **识别半径**: 20 米（用于筛选经过起终点的公交车）
- **走廊宽度**: 30 米（左右各 15 米）
- **分段长度**: 30 米（优化后）
- **总段数**: 18 段（优化后）

## 数据筛选结果

从上行数据文件夹中成功识别出 **13 辆公交车** 经过指定路段：

- CarNum_23178, CarNum_23484, CarNum_23485, CarNum_23486, CarNum_23487
- CarNum_23488, CarNum_23490, CarNum_23491, CarNum_23492, CarNum_23493
- CarNum_23494, CarNum_23497, CarNum_23498

所有筛选出的数据已保存到 `filtered_route_data/` 文件夹中。

## 速度分析结果

### 整体统计

- **有效分析段数**: 11 段（全部段都有数据）
- **平均速度范围**: 3.51 - 29.45 km/h
- **整体平均速度**: 14.56 km/h

### 各段详细分析

| 段 ID | 平均速度(km/h) | 数据点数量 | 速度特征 |
| ----- | -------------- | ---------- | -------- |
| 1     | 6.58           | 3,067      | 较慢     |
| 2     | 15.97          | 1,143      | 中等     |
| 3     | 28.45          | 643        | 较快     |
| 4     | 29.45          | 596        | 最快     |
| 5     | 18.96          | 993        | 中等偏快 |
| 6     | 11.44          | 1,575      | 中等偏慢 |
| 7     | 3.51           | 4,924      | 最慢     |
| 8     | 12.06          | 1,416      | 中等偏慢 |
| 9     | 5.94           | 2,999      | 较慢     |
| 10    | 6.69           | 2,652      | 较慢     |
| 11    | 21.12          | 978        | 中等偏快 |

## 关键发现

1. **速度变化明显**: 各段之间速度差异较大，最快段（段 4）比最慢段（段 7）快 8.4 倍
2. **低速区域集中**: 段 1、段 7、段 9、段 10 为相对低速区域（<10 km/h）
3. **高速区域**: 段 3、段 4 为高速区域（>25 km/h）
4. **数据密度**: 段 7 的数据点最多（4,924 个），可能是交通拥堵或停车区域

## 可能的交通特征分析

- **段 1**: 起点区域，速度较慢，可能是起步或等待区域
- **段 3-4**: 中段高速区域，道路条件较好，交通流畅
- **段 7**: 速度最慢且数据点最多，可能存在交通信号灯或拥堵点
- **段 9-10**: 接近终点的低速区域，可能是减速或停车准备区域
- **段 11**: 终点前的加速区域

## 输出文件

1. **交互式地图**: `speed_analysis_results/speed_distribution_map.html`

   - 显示路段分段和速度颜色编码
   - 包含起终点标记和图例
   - 可点击查看各段详细信息

2. **速度分布图表**: `speed_analysis_results/speed_distribution_chart.png`

   - 条形图显示各段平均速度
   - 颜色编码表示速度等级
   - 包含数据点数量标注

3. **详细数据**: `speed_analysis_results/speed_distribution_data.csv`

   - 包含各段的起终点坐标
   - 平均速度和数据点数量
   - 可用于进一步分析

4. **筛选数据**: `filtered_route_data/`
   - 包含所有经过指定路段的公交车原始数据
   - 可用于更深入的个体分析

## 建议

1. 对低速段（特别是段 7）进行实地调研，确认是否存在交通瓶颈
2. 分析高速段的道路特征，为其他路段改善提供参考
3. 考虑时间因素，分析不同时段的速度变化模式
4. 结合实际道路条件（信号灯、路口、坡度等）进行综合分析
