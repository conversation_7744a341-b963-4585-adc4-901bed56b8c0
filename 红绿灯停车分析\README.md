# 停止线坐标可视化程序

这个程序可以读取Excel文件中的经纬度坐标，并在交互式地图上进行可视化展示。

## 文件说明

### 主要程序文件
- `coordinate_visualization.py` - 完整功能的可视化程序
- `simple_map_viewer.py` - 简化版地图查看器
- `停止线点坐标.xlsx` - 包含坐标数据的Excel文件

### 生成的地图文件
- `coordinate_map.html` - 普通标记地图
- `coordinate_cluster_map.html` - 聚类标记地图
- `simple_map.html` - 简化版地图

## 功能特点

### 完整版程序 (coordinate_visualization.py)
- ✅ 读取Excel文件中的经纬度坐标
- ✅ 自动计算地图中心点
- ✅ 根据方向使用不同颜色标记（下行=红色，上行=蓝色）
- ✅ 交互式弹出窗口显示详细信息
- ✅ 包含图例说明
- ✅ 支持全屏查看
- ✅ 包含测量工具
- ✅ 生成聚类地图版本
- ✅ 自动在浏览器中打开

### 简化版程序 (simple_map_viewer.py)
- ✅ 快速生成基本地图
- ✅ 简洁的代码结构
- ✅ 易于修改和扩展

## 使用方法

### 方法1：运行完整版程序
```bash
python coordinate_visualization.py
```

### 方法2：运行简化版程序
```bash
python simple_map_viewer.py
```

## 数据格式要求

Excel文件应包含以下列：
- `方向` - 行驶方向（如：下行、上行）
- `停止线名称` - 停止线的名称
- `经度` - 经度坐标
- `纬度` - 纬度坐标

## 依赖库

程序需要以下Python库：
- `pandas` - 用于读取Excel文件
- `folium` - 用于生成交互式地图
- `webbrowser` - 用于自动打开浏览器

安装命令：
```bash
pip install pandas folium
```

## 地图功能

生成的HTML地图支持：
- 🔍 缩放和平移
- 📍 点击标记查看详细信息
- 🎯 悬停显示快速信息
- 📏 测量距离（完整版）
- 🖥️ 全屏查看（完整版）
- 📊 标记聚类（聚类版本）

## 坐标信息

当前数据包含16个停止线坐标点：
- 经度范围: 106.299288° ~ 106.341349°
- 纬度范围: 29.561964° ~ 29.613985°
- 地图中心: 纬度 29.590741°, 经度 106.314996°

## 自定义修改

### 修改标记颜色
在程序中找到 `direction_colors` 字典，可以修改不同方向的颜色：
```python
direction_colors = {
    '下行': 'red',
    '上行': 'blue',
    '其他': 'green'
}
```

### 修改地图样式
可以更改地图的瓦片样式：
```python
tiles='OpenStreetMap'  # 可选: 'Stamen Terrain', 'Stamen Toner', 'CartoDB positron'
```

### 修改缩放级别
调整初始缩放级别：
```python
zoom_start=13  # 数值越大，缩放越近
```

## 故障排除

1. **文件找不到错误**：确保Excel文件在程序同一目录下
2. **库导入错误**：运行 `pip install pandas folium` 安装依赖
3. **地图不显示**：检查生成的HTML文件是否存在，手动双击打开

## 扩展功能建议

- 添加路径连线功能
- 支持多种数据格式（CSV、JSON等）
- 添加热力图显示
- 集成实时交通信息
- 添加导出功能（PNG、PDF等）
