import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from shapely.geometry import Point, LineString, Polygon
from shapely.ops import transform, split
import pyproj
from geopy.distance import geodesic
import folium
from folium import plugins
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SpeedDistributionAnalyzer:
    def __init__(self, start_point, end_point, data_folder):
        """
        初始化速度分布分析器
        
        Args:
            start_point: 起点坐标 (lat, lon)
            end_point: 终点坐标 (lat, lon)
            data_folder: 数据文件夹路径
        """
        self.start_point = start_point
        self.end_point = end_point
        self.data_folder = data_folder
        self.filtered_data = []
        self.route_line = None
        self.route_polygon = None
        self.segments = []
        
    def calculate_distance(self, point1, point2):
        """计算两点间距离（米）"""
        return geodesic(point1, point2).meters
    
    def point_to_line_distance(self, point, line_start, line_end):
        """计算点到线段的距离"""
        # 使用投影坐标系进行精确计算
        transformer = pyproj.Transformer.from_crs('EPSG:4326', 'EPSG:3857', always_xy=True)
        
        # 转换为投影坐标
        line_start_proj = transformer.transform(line_start[1], line_start[0])
        line_end_proj = transformer.transform(line_end[1], line_end[0])
        point_proj = transformer.transform(point[1], point[0])
        
        # 创建线段
        line = LineString([line_start_proj, line_end_proj])
        point_geom = Point(point_proj)
        
        return line.distance(point_geom)
    
    def filter_buses_through_route(self, radius=20):
        """
        步骤1: 识别经过起终点范围的公交车数据
        """
        print("正在筛选经过指定路段的公交车数据...")
        
        # 创建输出文件夹
        output_folder = "filtered_route_data"
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        
        filtered_files = []
        
        for filename in os.listdir(self.data_folder):
            if filename.endswith('.txt') and filename.startswith('CarNum_'):
                filepath = os.path.join(self.data_folder, filename)
                
                try:
                    # 读取数据
                    df = pd.read_csv(filepath)
                    
                    # 检查是否经过起点和终点
                    passed_start = False
                    passed_end = False
                    
                    for _, row in df.iterrows():
                        point = (row['Latitude'], row['Longitude'])
                        
                        # 检查是否在起点范围内
                        if self.calculate_distance(point, self.start_point) <= radius:
                            passed_start = True
                        
                        # 检查是否在终点范围内
                        if self.calculate_distance(point, self.end_point) <= radius:
                            passed_end = True
                        
                        if passed_start and passed_end:
                            break
                    
                    # 如果经过起终点，保存数据
                    if passed_start and passed_end:
                        output_path = os.path.join(output_folder, filename)
                        df.to_csv(output_path, index=False)
                        filtered_files.append(filename)
                        self.filtered_data.append(df)
                        print(f"✓ {filename} 经过指定路段，已保存")
                    
                except Exception as e:
                    print(f"处理文件 {filename} 时出错: {e}")
        
        print(f"\n共找到 {len(filtered_files)} 辆公交车经过指定路段")
        return filtered_files
    
    def create_route_corridor(self, width=15):
        """
        步骤2: 创建路线走廊多边形
        """
        print("正在创建路线走廊...")
        
        # 创建起终点连线
        self.route_line = LineString([(self.start_point[1], self.start_point[0]), 
                                     (self.end_point[1], self.end_point[0])])
        
        # 使用投影坐标系创建缓冲区
        transformer_to_proj = pyproj.Transformer.from_crs('EPSG:4326', 'EPSG:3857', always_xy=True)
        transformer_to_geo = pyproj.Transformer.from_crs('EPSG:3857', 'EPSG:4326', always_xy=True)
        
        # 转换为投影坐标
        route_line_proj = transform(transformer_to_proj.transform, self.route_line)
        
        # 创建缓冲区（左右各width米）
        buffer_proj = route_line_proj.buffer(width)
        
        # 转换回地理坐标
        self.route_polygon = transform(transformer_to_geo.transform, buffer_proj)
        
        print(f"路线走廊创建完成，宽度: {width*2}米")
        return self.route_polygon

    def create_segments(self, segment_length=30):
        """
        步骤3: 将路线走廊分段
        """
        print(f"正在将路线分段，每段长度: {segment_length}米...")

        # 计算路线总长度
        route_length = geodesic(self.start_point, self.end_point).meters
        num_segments = int(np.ceil(route_length / segment_length))

        self.segments = []

        for i in range(num_segments):
            # 计算当前段的起终点
            start_ratio = i / num_segments
            end_ratio = min((i + 1) / num_segments, 1.0)

            # 线性插值计算段的起终点坐标
            seg_start_lat = self.start_point[0] + (self.end_point[0] - self.start_point[0]) * start_ratio
            seg_start_lon = self.start_point[1] + (self.end_point[1] - self.start_point[1]) * start_ratio
            seg_end_lat = self.start_point[0] + (self.end_point[0] - self.start_point[0]) * end_ratio
            seg_end_lon = self.start_point[1] + (self.end_point[1] - self.start_point[1]) * end_ratio

            segment_info = {
                'id': i,
                'start': (seg_start_lat, seg_start_lon),
                'end': (seg_end_lat, seg_end_lon),
                'center': ((seg_start_lat + seg_end_lat) / 2, (seg_start_lon + seg_end_lon) / 2),
                'speeds': [],
                'zero_speed_points': []  # 新增：存储速度为0的点
            }

            self.segments.append(segment_info)

        print(f"路线分段完成，共 {len(self.segments)} 段")
        return self.segments

    def calculate_segment_speeds(self, corridor_width=15):
        """
        计算每段的平均速度
        """
        print("正在计算各段平均速度...")

        # 为每个段分配GPS点和速度
        for df in self.filtered_data:
            for _, row in df.iterrows():
                point = (row['Latitude'], row['Longitude'])
                speed = row['GpsSpeed']

                # 检查点是否在路线走廊内
                point_geom = Point(point[1], point[0])
                if self.route_polygon.contains(point_geom):
                    # 找到最近的段
                    min_distance = float('inf')
                    closest_segment = None

                    for segment in self.segments:
                        # 计算点到段中心的距离
                        distance = self.calculate_distance(point, segment['center'])
                        if distance < min_distance:
                            min_distance = distance
                            closest_segment = segment

                    # 将速度添加到最近的段
                    if closest_segment and min_distance <= corridor_width * 2:
                        closest_segment['speeds'].append(speed)
                        # 如果速度为0，单独记录这些点
                        if speed == 0:
                            closest_segment['zero_speed_points'].append(point)

        # 计算每段的平均速度
        for segment in self.segments:
            if segment['speeds']:
                segment['avg_speed'] = np.mean(segment['speeds'])
                segment['speed_count'] = len(segment['speeds'])
                segment['zero_speed_count'] = len(segment['zero_speed_points'])
            else:
                segment['avg_speed'] = 0
                segment['speed_count'] = 0
                segment['zero_speed_count'] = 0

        print("各段平均速度计算完成")
        return self.segments

    def visualize_results(self, output_folder="speed_analysis_results"):
        """
        步骤4: 可视化结果
        """
        print("正在生成可视化结果...")

        # 创建输出文件夹
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        # 1. 创建Folium地图可视化
        m = folium.Map(
            location=[(self.start_point[0] + self.end_point[0]) / 2,
                      (self.start_point[1] + self.end_point[1]) / 2],
            zoom_start=15,
            tiles='OpenStreetMap'
        )

        # 添加起点和终点标记
        folium.Marker(
            location=[self.start_point[0], self.start_point[1]],
            popup='起点',
            icon=folium.Icon(color='green', icon='play')
        ).add_to(m)

        folium.Marker(
            location=[self.end_point[0], self.end_point[1]],
            popup='终点',
            icon=folium.Icon(color='red', icon='stop')
        ).add_to(m)

        # 创建基于5km/h间隔的颜色映射
        def get_speed_color(speed_kmh):
            """根据速度返回对应颜色（每5km/h一个颜色）"""
            speed_ranges = [
                (0, 5, '#8B0000'),      # 深红色 0-5 km/h
                (5, 10, '#FF0000'),     # 红色 5-10 km/h
                (10, 15, '#FF4500'),    # 橙红色 10-15 km/h
                (15, 20, '#FFA500'),    # 橙色 15-20 km/h
                (20, 25, '#FFD700'),    # 金色 20-25 km/h
                (25, 30, '#ADFF2F'),    # 绿黄色 25-30 km/h
                (30, 35, '#32CD32'),    # 酸橙绿 30-35 km/h
                (35, 40, '#228B22'),    # 森林绿 35-40 km/h
                (40, 45, '#006400'),    # 深绿色 40-45 km/h
                (45, float('inf'), '#004000')  # 极深绿色 45+ km/h
            ]

            for min_speed, max_speed, color in speed_ranges:
                if min_speed <= speed_kmh < max_speed:
                    return color, f"{min_speed}-{max_speed if max_speed != float('inf') else '45+'} km/h"
            return '#808080', '未知'  # 灰色作为默认

        # 获取所有有效段的速度
        valid_speeds = [segment['avg_speed'] for segment in self.segments if segment['speed_count'] > 0]

        if valid_speeds:
            # 添加每个段的多边形
            for segment in self.segments:
                if segment['speed_count'] > 0:
                    # 根据速度确定颜色
                    color, speed_range = get_speed_color(segment['avg_speed'])

                    # 创建段的线
                    folium.PolyLine(
                        locations=[
                            [segment['start'][0], segment['start'][1]],
                            [segment['end'][0], segment['end'][1]]
                        ],
                        color=color,
                        weight=8,
                        opacity=0.8,
                        popup=f"段 {segment['id']+1}: 平均速度 {segment['avg_speed']:.2f} km/h<br>"
                              f"速度范围: {speed_range}<br>"
                              f"总数据点: {segment['speed_count']} 个<br>"
                              f"零速度点: {segment['zero_speed_count']} 个"
                    ).add_to(m)

                    # 添加速度为0的点（70%透明度的小点）
                    for zero_point in segment['zero_speed_points']:
                        folium.CircleMarker(
                            location=[zero_point[0], zero_point[1]],
                            radius=2,
                            color='black',
                            fillColor='red',
                            fillOpacity=0.3,  # 70%透明度
                            opacity=0.7,
                            popup="速度为0的点"
                        ).add_to(m)

        # 创建详细的图例，包含各段数据点数量
        legend_items = []
        legend_items.append('<p><b>速度图例 (每5km/h一个颜色)</b></p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #8B0000"></i> 0-5 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FF0000"></i> 5-10 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FF4500"></i> 10-15 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FFA500"></i> 15-20 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FFD700"></i> 20-25 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #ADFF2F"></i> 25-30 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #32CD32"></i> 30-35 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #228B22"></i> 35-40 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #006400"></i> 40-45 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #004000"></i> 45+ km/h</p>')
        legend_items.append('<hr>')
        legend_items.append('<p><b>各段数据点数量:</b></p>')

        for segment in self.segments:
            if segment['speed_count'] > 0:
                legend_items.append(f'<p>段{segment["id"]+1}: {segment["speed_count"]}点 (零速度:{segment["zero_speed_count"]})</p>')

        legend_items.append('<hr>')
        legend_items.append('<p><i class="fa fa-circle" style="color: red; opacity: 0.3"></i> 速度为0的点 (70%透明度)</p>')

        legend_html = f'''
        <div style="position: fixed; bottom: 50px; left: 50px; z-index: 1000; background-color: white;
                    padding: 10px; border: 2px solid grey; border-radius: 5px; max-height: 400px; overflow-y: auto; font-size: 12px;">
            {"".join(legend_items)}
        </div>
        '''
        m.get_root().html.add_child(folium.Element(legend_html))

        # 保存地图
        map_path = os.path.join(output_folder, "speed_distribution_map.html")
        m.save(map_path)
        print(f"交互式地图已保存至: {map_path}")

        # 2. 创建Matplotlib条形图
        plt.figure(figsize=(12, 6))

        segment_ids = [f"段{i+1}" for i in range(len(self.segments))]
        avg_speeds = [segment['avg_speed'] if segment['speed_count'] > 0 else 0 for segment in self.segments]

        # 使用新的颜色映射函数
        colors = []
        for speed in avg_speeds:
            if speed > 0:
                color, _ = get_speed_color(speed)
                colors.append(color)
            else:
                colors.append('gray')

        bars = plt.bar(segment_ids, avg_speeds, color=colors)
        plt.xlabel('路段')
        plt.ylabel('平均速度 (km/h)')
        plt.title('各路段平均速度分布')
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 添加数据点数量标签
        for i, bar in enumerate(bars):
            count = self.segments[i]['speed_count']
            if count > 0:
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                         f"{count}点", ha='center', va='bottom', fontsize=8)

        # 保存图表
        chart_path = os.path.join(output_folder, "speed_distribution_chart.png")
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"速度分布图表已保存至: {chart_path}")

        # 3. 创建速度区间分布柱状图
        self.create_speed_interval_chart(output_folder)

        # 3. 导出数据到CSV
        results_df = pd.DataFrame({
            '段ID': [i+1 for i in range(len(self.segments))],
            '起点纬度': [segment['start'][0] for segment in self.segments],
            '起点经度': [segment['start'][1] for segment in self.segments],
            '终点纬度': [segment['end'][0] for segment in self.segments],
            '终点经度': [segment['end'][1] for segment in self.segments],
            '平均速度(km/h)': [segment['avg_speed'] if segment['speed_count'] > 0 else 0 for segment in self.segments],
            '数据点数量': [segment['speed_count'] for segment in self.segments]
        })

        csv_path = os.path.join(output_folder, "speed_distribution_data.csv")
        results_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"分析数据已保存至: {csv_path}")

        return map_path, chart_path, csv_path

    def create_speed_interval_chart(self, output_folder):
        """
        创建速度区间分布柱状图（每5km/h一个区间）
        """
        print("正在创建速度区间分布图...")

        # 收集所有速度数据点
        all_speeds = []
        for segment in self.segments:
            all_speeds.extend(segment['speeds'])

        if not all_speeds:
            print("没有速度数据，跳过速度区间分布图")
            return None

        # 定义速度区间（每5km/h一个区间）
        max_speed = max(all_speeds)
        intervals = []
        interval_counts = []
        interval_labels = []

        # 创建区间
        current_speed = 0
        while current_speed <= max_speed + 5:
            next_speed = current_speed + 5

            # 计算在这个区间内的点数
            count = sum(1 for speed in all_speeds if current_speed <= speed < next_speed)

            intervals.append((current_speed, next_speed))
            interval_counts.append(count)

            if next_speed > max_speed + 5:
                interval_labels.append(f"{current_speed}+ km/h")
            else:
                interval_labels.append(f"{current_speed}-{next_speed} km/h")

            current_speed = next_speed

            # 避免无限循环
            if current_speed > 100:
                break

        # 创建柱状图
        plt.figure(figsize=(12, 8))

        # 使用与地图相同的颜色方案
        colors = ['#8B0000', '#FF0000', '#FF4500', '#FFA500', '#FFD700',
                 '#ADFF2F', '#32CD32', '#228B22', '#006400', '#004000']

        # 确保颜色数量足够
        while len(colors) < len(interval_counts):
            colors.append('#004000')  # 使用最深的绿色

        bars = plt.bar(range(len(interval_counts)), interval_counts,
                      color=colors[:len(interval_counts)], alpha=0.8, edgecolor='black')

        plt.xlabel('速度区间 (km/h)', fontsize=12)
        plt.ylabel('数据点数量', fontsize=12)
        plt.title('速度区间分布统计\n(每5km/h一个区间)', fontsize=14, fontweight='bold')
        plt.xticks(range(len(interval_labels)), interval_labels, rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)

        # 在每个柱子上添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            if height > 0:
                plt.text(bar.get_x() + bar.get_width()/2, height + max(interval_counts)*0.01,
                        f'{int(height)}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图表
        interval_chart_path = os.path.join(output_folder, "speed_interval_distribution.png")
        plt.savefig(interval_chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"速度区间分布图已保存至: {interval_chart_path}")

        # 输出统计信息
        print("\n速度区间统计:")
        total_points = sum(interval_counts)
        for i, (interval, count) in enumerate(zip(intervals, interval_counts)):
            if count > 0:
                percentage = (count / total_points) * 100
                print(f"{interval_labels[i]}: {count} 个点 ({percentage:.1f}%)")

        return interval_chart_path

    def run_complete_analysis(self):
        """
        运行完整的速度分布分析流程
        """
        print("开始速度分布分析...")
        print(f"起点: {self.start_point}")
        print(f"终点: {self.end_point}")
        print(f"数据文件夹: {self.data_folder}")
        print("-" * 50)

        # 步骤1: 筛选经过路段的公交车
        filtered_files = self.filter_buses_through_route(radius=20)

        if not filtered_files:
            print("未找到经过指定路段的公交车数据！")
            return None

        # 步骤2: 创建路线走廊
        self.create_route_corridor(width=15)

        # 步骤3: 分段并计算速度
        self.create_segments(segment_length=30)
        self.calculate_segment_speeds(corridor_width=15)

        # 步骤4: 可视化结果
        map_path, chart_path, csv_path = self.visualize_results()

        # 输出分析摘要
        print("\n" + "="*50)
        print("分析完成摘要:")
        print(f"- 筛选出 {len(filtered_files)} 辆公交车的数据")
        print(f"- 路线总长度: {geodesic(self.start_point, self.end_point).meters:.1f} 米")
        print(f"- 分为 {len(self.segments)} 个段")

        valid_segments = [s for s in self.segments if s['speed_count'] > 0]
        if valid_segments:
            avg_speeds = [s['avg_speed'] for s in valid_segments]
            print(f"- 有效段数: {len(valid_segments)}")
            print(f"- 平均速度范围: {min(avg_speeds):.2f} - {max(avg_speeds):.2f} km/h")
            print(f"- 整体平均速度: {np.mean(avg_speeds):.2f} km/h")

        print(f"\n输出文件:")
        print(f"- 交互式地图: {map_path}")
        print(f"- 速度分布图: {chart_path}")
        print(f"- 数据文件: {csv_path}")
        print("="*50)

        return {
            'filtered_files': filtered_files,
            'segments': self.segments,
            'map_path': map_path,
            'chart_path': chart_path,
            'csv_path': csv_path
        }


def main():
    """
    主函数 - 执行速度分布分析
    """
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        data_folder = sys.argv[1]
    else:
        data_folder = "上行数据"  # 默认使用上行数据

    print(f"正在分析数据文件夹: {data_folder}")

    # 定义起终点坐标
    start_point = (29.585958, 106.314104)  # (纬度, 经度)
    end_point = (29.590679, 106.314488)    # (纬度, 经度)

    # 创建分析器实例
    analyzer = SpeedDistributionAnalyzer(start_point, end_point, data_folder)

    # 运行完整分析
    results = analyzer.run_complete_analysis()

    if results:
        print("\n分析成功完成！")
        print("请查看生成的文件以获取详细结果。")
    else:
        print("\n分析失败，请检查数据和参数设置。")


if __name__ == "__main__":
    main()
