import pandas as pd
import numpy as np
from math import radians, sin, cos, sqrt, atan2
import folium
import glob
import os
from datetime import datetime, time
import io

# 1. 设置路径并获取所有txt文件
base_path = '../上行数据'
try:
    # 获取所有txt文件路径
    all_txt_files = glob.glob(os.path.join(base_path, '*.txt'))
    
    if all_txt_files:
        print(f"找到 {len(all_txt_files)} 个txt文件:")
        for f in all_txt_files[:5]:  # 只显示前5个文件名
            print(f"- {os.path.basename(f)}")
        if len(all_txt_files) > 5:
            print(f"... 还有 {len(all_txt_files) - 5} 个文件")
    else:
        print(f"错误：在 '{base_path}' 路径下没有找到任何txt文件。")

except Exception as e:
    print(f"错误：无法访问文件夹: {e}")
    all_txt_files = []

# 定义时间段
def get_time_period(time_str):
    """根据时间字符串判断属于哪个时间段"""
    try:
        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        hour = dt.hour
        
        if 7 <= hour < 9:  # 早高峰 7:00-9:00
            return 'morning_peak'
        elif 10 <= hour < 16:  # 平峰 10:00-16:00
            return 'midday_peak'
        elif 17 <= hour < 19:  # 晚高峰 17:00-19:00
            return 'evening_peak'
        else:
            return None
    except:
        return None

print("时间段定义:")
print("- 早高峰: 7:00-9:00")
print("- 平峰: 10:00-16:00")
print("- 晚高峰: 17:00-19:00")

# 辅助函数：计算两点之间的距离（Haversine公式）
def haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    return c * 6371000 # 地球半径（米）

# 辅助函数：根据速度返回颜色
def speed_to_color(speed):
    if speed == 0:
        return 'purple' # 速度为0
    elif 0 < speed <= 10:
        return 'red'
    elif 10 < speed <= 20:
        return 'orange'
    elif 20 < speed <= 30:
        return 'blue'
    else: # speed > 30
        return 'green'

# 站点数据
station_data = """
0        陈家桥新医院  106.339491  29.617029
1          陈南路口  106.341308  29.611608
2     大学城北路东段路口  106.335350  29.610001
3        轨道陈家桥站  106.324727  29.610164
4        大学城东路东  106.316293  29.608722
5         大学城东路  106.315368  29.602679
6       大学城东路3站  106.314804  29.596149
7      重庆科技大学西门  106.314510  29.591357
8       大学城东路4站  106.314295  29.588785
9       大学城东路南段  106.313775  29.583780
10         康居西城  106.312862  29.580126
11       康居西城花市  106.309097  29.579152
12         康城南路  106.306724  29.575119
13          英业达  106.303621  29.565268
14         曾家转盘  106.299375  29.560237
"""

stations_df = pd.read_csv(io.StringIO(station_data), sep=r'\s+', header=None,
                          names=['index', 'station_name', 'longitude', 'latitude'])
print("站点数据加载成功:")
print(stations_df.head())

# 核心处理函数：加载、清洗、处理数据并生成地图
def process_and_generate_map(file_paths, time_period, output_filename):
    print(f"\n--- 开始处理并生成地图: {output_filename} ({time_period}) ---")

    # 1. 加载数据并按时间段过滤
    li = []
    for filename in file_paths:
        if not os.path.exists(filename):
            print(f"警告：文件不存在，跳过: {filename}")
            continue

        # 从文件名提取车辆ID
        vehicle_id = os.path.basename(filename).replace('.txt', '').replace('CarNum_', '')

        try:
            df_file = pd.read_csv(filename, index_col=None, header=0)
            if not df_file.empty:
                # 添加车辆ID
                df_file['vehicle_id'] = vehicle_id

                # 根据时间段过滤数据
                df_file['time_period'] = df_file['GpsTime'].apply(get_time_period)
                df_filtered = df_file[df_file['time_period'] == time_period]

                if not df_filtered.empty:
                    li.append(df_filtered)

        except Exception as e:
            print(f"读取文件 {filename} 时出错: {e}")

    if not li:
        print(f"错误：在 {time_period} 时间段内未能加载任何数据。")
        return

    df = pd.concat(li, axis=0, ignore_index=True)
    print(f"从 {len(li)} 个文件中加载了 {len(df)} 行 {time_period} 时间段的数据。")
    print("处理的车辆ID:", df['vehicle_id'].unique())

    # 2. 剔除异常点
    Q1 = df[['Longitude', 'Latitude']].quantile(0.25)
    Q3 = df[['Longitude', 'Latitude']].quantile(0.75)
    IQR = Q3 - Q1
    df_no_outliers = df[~((df[['Longitude', 'Latitude']] < (Q1 - 1.5 * IQR)) |(df[['Longitude', 'Latitude']] > (Q3 + 1.5 * IQR))).any(axis=1)]
    print(f"原始数据量: {len(df)}")
    print(f"剔除异常点后数据量: {len(df_no_outliers)}")
    if df_no_outliers.empty:
        print("剔除异常点后没有剩余数据，无法生成地图。")
        return

    # 3. 划分路段并处理长时间停车
    all_segments = []
    all_stationary_points = []
    all_short_stationary_points = []
    grouped = df_no_outliers.groupby('vehicle_id')

    for vehicle_id, vehicle_df in grouped:
        segments = []
        stationary_points = []
        short_stationary_points = []
        start_index = 0
        zero_speed_count = 0
        vehicle_df = vehicle_df.reset_index(drop=True)

        MAX_ADJACENT_DISTANCE = 500

        for i in range(1, len(vehicle_df)):
            lon1, lat1 = vehicle_df.iloc[i-1]['Longitude'], vehicle_df.iloc[i-1]['Latitude']
            lon2, lat2 = vehicle_df.iloc[i]['Longitude'], vehicle_df.iloc[i]['Latitude']

            adjacent_distance = haversine(lon1, lat1, lon2, lat2)

            if adjacent_distance > MAX_ADJACENT_DISTANCE:
                if start_index < i:
                    segments.append(vehicle_df.iloc[start_index:i])
                start_index = i
                zero_speed_count = 0
                continue

            is_same_location = (vehicle_df.iloc[i]['Longitude'] == vehicle_df.iloc[i-1]['Longitude'] and
                                vehicle_df.iloc[i]['Latitude'] == vehicle_df.iloc[i-1]['Latitude'])

            if vehicle_df.iloc[i]['GpsSpeed'] == 0 and is_same_location:
                zero_speed_count += 1
            else:
                if zero_speed_count > 6: # 超过6个点（1分钟）认为是长时间停留
                    stationary_points.append(vehicle_df.iloc[i - zero_speed_count])
                    if start_index < i - zero_speed_count:
                         segments.append(vehicle_df.iloc[start_index:i-zero_speed_count])
                    start_index = i
                elif 3 <= zero_speed_count <= 6: # 3到6个点认为是短时间停留
                    short_stationary_points.append(vehicle_df.iloc[i - zero_speed_count])
                    if start_index < i - zero_speed_count:
                        segments.append(vehicle_df.iloc[start_index:i-zero_speed_count])
                    start_index = i
                zero_speed_count = 0

            if start_index < i:
                distance = haversine(vehicle_df.iloc[start_index]['Longitude'], vehicle_df.iloc[start_index]['Latitude'],
                                     vehicle_df.iloc[i]['Longitude'], vehicle_df.iloc[i]['Latitude'])
                if distance >= 100:
                    segments.append(vehicle_df.iloc[start_index:i+1])
                    start_index = i + 1

        if start_index < len(vehicle_df):
            segments.append(vehicle_df.iloc[start_index:])

        all_segments.extend(segments)
        all_stationary_points.extend(stationary_points)
        all_short_stationary_points.extend(short_stationary_points)

    print(f"所有车辆的线路总共被划分为 {len(all_segments)} 个路段")
    print(f"所有车辆总共检测到 {len(all_stationary_points)} 个长时间停留点")
    print(f"所有车辆总共检测到 {len(all_short_stationary_points)} 个短时间停留点")

    # 4. 计算每个路段的速度平均值
    segment_avg_speeds = [segment['GpsSpeed'].mean() for segment in all_segments if not segment.empty]

    # 5. 使用folium在地图上可视化
    center_lon = df_no_outliers['Longitude'].mean()
    center_lat = df_no_outliers['Latitude'].mean()
    m = folium.Map(location=[center_lat, center_lon], zoom_start=12)

    # 在地图上标记站点
    if not stations_df.empty:
        for idx, row in stations_df.iterrows():
            folium.Marker(
                location=[row['latitude'], row['longitude']],
                popup=f"{row['station_name']}",
                icon=folium.Icon(color='blue', icon='bus', prefix='fa')
            ).add_to(m)
        print(f"已在地图上标记 {len(stations_df)} 个站点。")

    # 绘制路段
    for i, segment in enumerate(all_segments):
        if not segment.empty and i < len(segment_avg_speeds):
            avg_speed = segment_avg_speeds[i]
            color = speed_to_color(avg_speed)
            points = segment[['Latitude', 'Longitude']].values.tolist()
            folium.PolyLine(points, color=color, weight=5, opacity=0.2, popup=f'平均速度: {avg_speed:.2f} km/h').add_to(m)

    # 标记短时间停留点
    for point in all_short_stationary_points:
        folium.CircleMarker(
            location=[point['Latitude'], point['Longitude']],
            radius=3, color='gray', fill=True, fill_color='gray', fill_opacity=0.1, popup='短时间停留'
        ).add_to(m)

    # 标记长时间停留点
    for point in all_stationary_points:
        folium.CircleMarker(
            location=[point['Latitude'], point['Longitude']],
            radius=3, color='black', fill=True, fill_color='black', fill_opacity=0.5, popup='长时间停留'
        ).add_to(m)

    # 添加图例
    legend_html = '''
    <div style="position: fixed;
         bottom: 50px; left: 50px; width: 220px; height: 170px;
         border:2px solid grey; z-index:9999; font-size:14px;
         background-color:white;
         ">&nbsp;<b>速度图例 (km/h)</b><br>
         &nbsp;<i class="fa fa-minus fa-1x" style="color:red"></i>&nbsp;0 &lt; 速度 &le; 10<br>
         &nbsp;<i class="fa fa-minus fa-1x" style="color:orange"></i>&nbsp;10 &lt; 速度 &le; 20<br>
         &nbsp;<i class="fa fa-minus fa-1x" style="color:blue"></i>&nbsp;20 &lt; 速度 &le; 30<br>
         &nbsp;<i class="fa fa-minus fa-1x" style="color:green"></i>&nbsp;速度 &gt; 30<br>
         &nbsp;<i class="fa fa-circle fa-1x" style="color:black"></i>&nbsp;长时间停留点<br>
         &nbsp;<i class="fa fa-circle fa-1x" style="color:gray"></i>&nbsp;短时间停留点
    </div>
    '''
    m.get_root().html.add_child(folium.Element(legend_html))

    # 保存地图
    m.save(output_filename)
    print(f"地图已成功保存至: {output_filename}")

# 主程序：处理所有时间段
if __name__ == "__main__":
    if all_txt_files:
        # 定义时间段和对应的输出文件名
        time_periods = {
            'morning_peak': 'upward_morning_peak_map.html',
            'midday_peak': 'upward_midday_peak_map.html',
            'evening_peak': 'upward_evening_peak_map.html'
        }

        # 处理每个时间段
        for time_period, output_filename in time_periods.items():
            process_and_generate_map(all_txt_files, time_period, output_filename)

        print("\n所有时间段处理完毕。")
        print("生成的地图文件:")
        for filename in time_periods.values():
            print(f"- {filename}")
    else:
        print("没有找到任何txt文件，无法继续处理。")
