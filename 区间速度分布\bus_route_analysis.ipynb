{"cells": [{"cell_type": "code", "execution_count": 6, "id": "505c39f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   CarNum         GpsTime  Longitude   Latitude  GpsSpeed\n", "0   23178  2024-11-1 0:03    0.00000   0.000000         0\n", "1   23178  2024-11-1 0:03  106.32799  29.630055         0\n", "2   23178  2024-11-1 0:03  106.32799  29.630055         0\n", "3   23178  2024-11-1 0:03  106.32799  29.630051         0\n", "4   23178  2024-11-1 0:03  106.32799  29.630051         0\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from math import radians, sin, cos, sqrt, atan2\n", "import folium\n", "\n", "# 加载数据\n", "df = pd.read_csv('car_23178.csv')\n", "\n", "# 打印前几行数据以供参考\n", "print(df.head())\n"]}, {"cell_type": "code", "execution_count": 7, "id": "0c697841", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据量: 97916\n", "剔除异常点后数据量: 97898\n"]}], "source": ["# 1. 剔除异常点\n", "# 使用IQR（四分位距）方法剔除经纬度异常值\n", "Q1 = df[['Longitude', 'Latitude']].quantile(0.25)\n", "Q3 = df[['Longitude', 'Latitude']].quantile(0.75)\n", "IQR = Q3 - Q1\n", "\n", "df_no_outliers = df[~((df[['Longitude', 'Latitude']] < (Q1 - 1.5 * IQR)) |(df[['Longitude', 'Latitude']] > (Q3 + 1.5 * IQR))).any(axis=1)]\n", "\n", "print(f\"原始数据量: {len(df)}\")\n", "print(f\"剔除异常点后数据量: {len(df_no_outliers)}\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "45a56e73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["线路被划分为 14068 个区间\n", "检测到 860 个长时间停留点\n", "检测到 1696 个短时间停留点\n"]}], "source": ["# 辅助函数：计算两点之间的距离（Haversine公式）\n", "def haversine(lon1, lat1, lon2, lat2):\n", "    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])\n", "    dlon = lon2 - lon1\n", "    dlat = lat2 - lat1\n", "    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2\n", "    c = 2 * atan2(sqrt(a), sqrt(1-a))\n", "    return c * 6371000 # 地球半径（米）\n", "\n", "# 2&4. 划分路段并处理长时间停车\n", "segments = []\n", "stationary_points = []\n", "short_stationary_points = [] # 新增：用于存储短时间停留点\n", "start_index = 0\n", "zero_speed_count = 0\n", "df_no_outliers = df_no_outliers.reset_index(drop=True) # 重置索引\n", "\n", "# 新增：定义相邻点之间的最大允许距离（米），可根据实际情况调整\n", "MAX_ADJACENT_DISTANCE = 500 \n", "\n", "for i in range(1, len(df_no_outliers)):\n", "    # 新增逻辑：检查相邻点之间的距离是否过大\n", "    lon1 = df_no_outliers.iloc[i-1]['Longitude']\n", "    lat1 = df_no_outliers.iloc[i-1]['Latitude']\n", "    lon2 = df_no_outliers.iloc[i]['Longitude']\n", "    lat2 = df_no_outliers.iloc[i]['Latitude']\n", "    \n", "    adjacent_distance = haversine(lon1, lat1, lon2, lat2)\n", "\n", "    if adjacent_distance > MAX_ADJACENT_DISTANCE:\n", "        # 如果距离过大，则在此处断开，创建一个新路段\n", "        if start_index < i:\n", "            segments.append(df_no_outliers.iloc[start_index:i])\n", "        start_index = i\n", "        zero_speed_count = 0 # 重置停留计数\n", "        continue # 继续处理下一个点\n", "\n", "    # 检查长时间停车 (原有逻辑)\n", "    is_same_location = (df_no_outliers.iloc[i]['Longitude'] == df_no_outliers.iloc[i-1]['Longitude'] and \n", "                        df_no_outliers.iloc[i]['Latitude'] == df_no_outliers.iloc[i-1]['Latitude'])\n", "\n", "    if df_no_outliers.iloc[i]['GpsSpeed'] == 0 and is_same_location:\n", "        zero_speed_count += 1\n", "    else:\n", "        if zero_speed_count > 6: # 长时间停留\n", "            # 将这个静止点序列的第一个点加入列表\n", "            stationary_points.append(df_no_outliers.iloc[i - zero_speed_count])\n", "            # 将这些点从路段划分中排除\n", "            if start_index < i - zero_speed_count:\n", "                 segments.append(df_no_outliers.iloc[start_index:i-zero_speed_count])\n", "            start_index = i \n", "        elif 3 <= zero_speed_count <= 6: # 短时间停留\n", "            # 将这个静止点序列的第一个点加入列表\n", "            short_stationary_points.append(df_no_outliers.iloc[i - zero_speed_count])\n", "            # 同样将这些点从路段划分中排除\n", "            if start_index < i - zero_speed_count:\n", "                 segments.append(df_no_outliers.iloc[start_index:i-zero_speed_count])\n", "            start_index = i\n", "        zero_speed_count = 0\n", "\n", "    # 根据距离划分路段 (原有逻辑)\n", "    if start_index < i: # 确保我们不会在静止点之后立即创建一个空段\n", "        distance = haversine(df_no_outliers.iloc[start_index]['Longitude'],\n", "                             df_no_outliers.iloc[start_index]['Latitude'],\n", "                             df_no_outliers.iloc[i]['Longitude'],\n", "                             df_no_outliers.iloc[i]['Latitude'])\n", "\n", "        if distance >= 100:\n", "            segments.append(df_no_outliers.iloc[start_index:i+1])\n", "            start_index = i + 1\n", "\n", "# 添加最后一个路段\n", "if start_index < len(df_no_outliers):\n", "    segments.append(df_no_outliers.iloc[start_index:])\n", "\n", "print(f\"线路被划分为 {len(segments)} 个区间\")\n", "print(f\"检测到 {len(stationary_points)} 个长时间停留点\")\n", "print(f\"检测到 {len(short_stationary_points)} 个短时间停留点\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "d3fda38d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.0, 0.0, 13.333333333333334, 8.666666666666666, 25.0]\n"]}], "source": ["# 3. 计算每个区间的速度平均值\n", "segment_avg_speeds = [segment['GpsSpeed'].mean() for segment in segments if not segment.empty]\n", "\n", "# 打印一些区间的平均速度以供参考\n", "print(segment_avg_speeds[:5])\n"]}, {"cell_type": "code", "execution_count": 10, "id": "e84b7e2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["新地图已保存至 bus_route_speed_map_v1.html\n"]}], "source": ["# 4. 使用folium在地图上可视化\n", "# 计算地图中心\n", "center_lon = df_no_outliers['Longitude'].mean()\n", "center_lat = df_no_outliers['Latitude'].mean()\n", "\n", "m = folium.Map(location=[center_lat, center_lon], zoom_start=13)\n", "\n", "# 颜色映射\n", "def speed_to_color(speed):\n", "    if speed == 0:\n", "        return 'purple' # 速度为0\n", "    elif 0 < speed <= 10:\n", "        return 'red'\n", "    elif 10 < speed <= 20:\n", "        return 'orange'\n", "    elif 20 < speed <= 30:\n", "        return 'blue'\n", "    else: # speed > 30\n", "        return 'green'\n", "\n", "# 绘制路段\n", "for i, segment in enumerate(segments):\n", "    if not segment.empty and i < len(segment_avg_speeds):\n", "        avg_speed = segment_avg_speeds[i]\n", "        color = speed_to_color(avg_speed)\n", "        points = segment[['Latitude', 'Longitude']].values.tolist()\n", "        folium.PolyLine(points, color=color, weight=5, opacity=0.2, popup=f'Avg Speed: {avg_speed:.2f} km/h').add_to(m)\n", "\n", "\n", "\n", "# 新增：标记短时间停留点\n", "for point in short_stationary_points:\n", "    folium.CircleMarker(\n", "        location=[point['Latitude'], point['Longitude']],\n", "        radius=3,\n", "        color='grey',\n", "        opacity=0.4,\n", "        fill=True,\n", "        fill_color='grey',\n", "        fill_opacity=0.2,\n", "        popup='短时间停留'\n", "    ).add_to(m)\n", "\n", "# 标记长时间停留点\n", "for point in stationary_points:\n", "    folium.CircleMarker(\n", "        location=[point['Latitude'], point['Longitude']],\n", "        radius=3,\n", "        color='black',\n", "        opacity=0.4,\n", "        fill=True,\n", "        fill_color='black',\n", "        fill_opacity=0.2,\n", "        popup='长时间停留'\n", "    ).add_to(m)\n", "\n", "# 添加图例\n", "legend_html = '''\n", "<div style=\"position: fixed; \n", "     bottom: 50px; left: 50px; width: 220px; height: 170px; \n", "     border:2px solid grey; z-index:9999; font-size:14px;\n", "     \">&nbsp;<b>速度图例 (km/h)</b><br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:red\"></i>&nbsp;0 &lt; 速度 &le; 10<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:orange\"></i>&nbsp;10 &lt; 速度 &le; 20<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:blue\"></i>&nbsp;20 &lt; 速度 &le; 30<br>\n", "     &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:green\"></i>&nbsp;速度 &gt; 30<br>\n", "     &nbsp;<i class=\"fa fa-circle fa-1x\" style=\"color:black\"></i>&nbsp;长时间停留点<br>\n", "     &nbsp;<i class=\"fa fa-circle fa-1x\" style=\"color:grey\"></i>&nbsp;短时间停留点\n", "</div>\n", "'''\n", "m.get_root().html.add_child(folium.Element(legend_html))\n", "\n", "\n", "# 保存地图\n", "m.save('bus_route_speed_map_v1.html')\n", "\n", "print(\"新地图已保存至 bus_route_speed_map_v1.html\")\n"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}