import graphviz

# --- Create a new Directed Graph ---
# 'LR' means Left-to-Right layout
dot = graphviz.Digraph('CUDA_Optimization_Mindmap', comment='CUDA课程思维导图')
dot.attr(rankdir='LR', splines='spline', overlap='false', fontname='Microsoft YaHei')

# --- Define Styles for Nodes ---
# Node attributes for different levels
root_node_style = {'shape': 'doublecircle', 'style': 'filled', 'fillcolor': '#87CEEB', 'fontname': 'Microsoft YaHei'}
topic_node_style = {'shape': 'box', 'style': 'rounded,filled', 'fillcolor': '#90EE90', 'fontname': 'Microsoft YaHei'}
detail_node_style = {'shape': 'box', 'style': 'rounded,filled', 'fillcolor': '#F5F5DC', 'fontname': 'Microsoft YaHei'}

# --- Create Nodes and Edges ---

# Root Node
dot.node('root', '性能模型与\n逐元素优化', root_node_style)

# Level 1 Topics
dot.node('topic1', '1. 问题提出与\n瓶颈分析', topic_node_style)
dot.edge('root', 'topic1')
dot.node('topic2', '2. 核心概念\n内存墙', topic_node_style)
dot.edge('root', 'topic2')
dot.node('topic3', '3. 分析模型\nRoofline模型', topic_node_style)
dot.edge('root', 'topic3')
dot.node('topic4', '4. 分析工具\nNsight Compute', topic_node_style)
dot.edge('root', 'topic4')
dot.node('topic5', '5. 优化策略一\n向量化', topic_node_style)
dot.edge('root', 'topic5')
dot.node('topic6', '6. 优化策略二\n半精度', topic_node_style)
dot.edge('root', 'topic6')

# Details for Topic 1: 问题提出
[cite_start]dot.node('t1_1', '回顾: GPU已有千倍加速 [cite: 14]', detail_node_style)
dot.edge('topic1', 't1_1')
[cite_start]dot.node('t1_2', '初步分析: 数据传输(HtoD/DtoH)\n耗时远超核函数 [cite: 85]', detail_node_style)
dot.edge('topic1', 't1_2')
[cite_start]dot.node('t1_3', '结论: CPU-GPU通信是主要瓶颈 [cite: 93]', detail_node_style)
dot.edge('topic1', 't1_3')

# Details for Topic 2: 内存墙
[cite_start]dot.node('t2_1', '定义: 算力增速远超内存带宽增速 [cite: 145, 147]', detail_node_style)
dot.edge('topic2', 't2_1')
[cite_start]dot.node('t2_2', '实例 (A100): 算力(19.5 TFLOPS)远高\n于内存带宽(~2039 GB/s) [cite: 98, 99]', detail_node_style)
dot.edge('topic2', 't2_2')
[cite_start]dot.node('t2_3', '影响: 访存密集型计算受限于内存速度 [cite: 147]', detail_node_style)
dot.edge('topic2', 't2_3')

# Details for Topic 3: Roofline模型
[cite_start]dot.node('t3_1', '用途: 可视化分析性能瓶颈 [cite: 203, 227]', detail_node_style)
dot.edge('topic3', 't3_1')
[cite_start]dot.node('t3_2', '构成: Y轴(性能) vs X轴(计算强度) [cite: 203]', detail_node_style)
dot.edge('topic3', 't3_2')
[cite_start]dot.node('t3_3', '区域: 访存受限 vs 计算受限 [cite: 227]', detail_node_style)
dot.edge('topic3', 't3_3')

# Details for Topic 4: Nsight Compute
[cite_start]dot.node('t4_1', '定义: 英伟达内核级性能分析工具 [cite: 244]', detail_node_style)
dot.edge('topic4', 't4_1')
[cite_start]dot.node('t4_2', '功能: 生成Roofline图、分析利用率等 [cite: 259, 261]', detail_node_style)
dot.edge('topic4', 't4_2')

# Details for Topic 5: 向量化
[cite_start]dot.node('t5_1', '目标: 增加单次数据传输量 [cite: 269, 270]', detail_node_style)
dot.edge('topic5', 't5_1')
[cite_start]dot.node('t5_2', '原理: SIMD (单指令多数据) [cite: 285]', detail_node_style)
dot.edge('topic5', 't5_2')
[cite_start]dot.node('t5_3', '实现: 使用float2, float4等向量化类型 [cite: 309]', detail_node_style)
dot.edge('topic5', 't5_3')
[cite_start]dot.node('t5_4', '机制一: 内存对齐\n(访问地址需为事务大小的倍数) [cite: 446]', detail_node_style)
dot.edge('topic5', 't5_4')
[cite_start]dot.node('t5_5', '机制二: 内存合并\n(Warp内访问连续、对齐内存时合并) [cite: 462]', detail_node_style)
dot.edge('topic5', 't5_5')

# Details for Topic 6: 半精度
[cite_start]dot.node('t6_1', '目标: 在Roofline图上"向右移动" [cite: 533, 540]', detail_node_style)
dot.edge('topic6', 't6_1')
[cite_start]dot.node('t6_2', '原理: 减少内存访问量(Q)以提高计算强度(AI) [cite: 536, 552]', detail_node_style)
dot.edge('topic6', 't6_2')
[cite_start]dot.node('t6_3', '实现: 使用half替代float, 结合half2向量化 [cite: 549, 588, 662]', detail_node_style)
dot.edge('topic6', 't6_3')
[cite_start]dot.node('t6_4', '双重收益: AI右移 + 硬件性能天花板抬高 [cite: 683, 684, 691]', detail_node_style)
dot.edge('topic6', 't6_4')

# --- Render and Save the Graph ---
# The 'view=True' argument will try to open the generated image automatically.
# The format can be changed to 'svg', 'pdf', etc.
output_filename = 'CUDA_Optimization_Mindmap.gv'
dot.render(output_filename, format='png', view=True)

print(f"Mind map has been generated and saved as '{output_filename}.png'")