import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from shapely.geometry import Point, LineString, Polygon
from shapely.ops import transform, split
import pyproj
from geopy.distance import geodesic
import folium
from folium import plugins
import matplotlib.colors as mcolors
from matplotlib.colors import LinearSegmentedColormap
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DualDirectionSpeedAnalyzer:
    def __init__(self, start_point, end_point, upward_folder, downward_folder):
        """
        初始化双向速度分布分析器
        
        Args:
            start_point: 起点坐标 (lat, lon)
            end_point: 终点坐标 (lat, lon)
            upward_folder: 上行数据文件夹路径
            downward_folder: 下行数据文件夹路径
        """
        self.start_point = start_point
        self.end_point = end_point
        self.upward_folder = upward_folder
        self.downward_folder = downward_folder
        
        # 存储两个方向的分析器
        self.upward_analyzer = None
        self.downward_analyzer = None
        
        # 存储分析结果
        self.upward_results = None
        self.downward_results = None
    
    def analyze_single_direction(self, data_folder, direction_name):
        """
        分析单个方向的数据
        """
        from speed_distribution_analysis import SpeedDistributionAnalyzer
        
        print(f"\n{'='*60}")
        print(f"开始分析{direction_name}数据...")
        print(f"{'='*60}")
        
        # 创建单向分析器
        analyzer = SpeedDistributionAnalyzer(self.start_point, self.end_point, data_folder)
        
        # 执行分析步骤
        filtered_files = analyzer.filter_buses_through_route(radius=20)
        if not filtered_files:
            print(f"{direction_name}未找到经过指定路段的公交车数据！")
            return None, None
        
        analyzer.create_route_corridor(width=15)
        analyzer.create_segments(segment_length=20)
        analyzer.calculate_segment_speeds(corridor_width=15)
        
        return analyzer, filtered_files
    
    def create_direction_outputs(self, analyzer, direction_name, output_base_folder):
        """
        为指定方向创建输出文件
        """
        direction_folder = os.path.join(output_base_folder, f"{direction_name}_results")
        if not os.path.exists(direction_folder):
            os.makedirs(direction_folder)
        
        print(f"正在为{direction_name}生成可视化结果...")
        
        # 1. 创建交互式地图
        map_path = self.create_interactive_map(analyzer, direction_name, direction_folder)
        
        # 2. 创建各段速度条形图
        chart_path = self.create_segment_chart(analyzer, direction_name, direction_folder)
        
        # 3. 创建速度区间分布图
        interval_chart_path = self.create_interval_chart(analyzer, direction_name, direction_folder)
        
        # 4. 导出详细数据CSV
        csv_path = self.export_detailed_data(analyzer, direction_name, direction_folder)
        
        return {
            'map_path': map_path,
            'chart_path': chart_path,
            'interval_chart_path': interval_chart_path,
            'csv_path': csv_path,
            'analyzer': analyzer
        }
    
    def create_interactive_map(self, analyzer, direction_name, output_folder):
        """
        创建交互式地图
        """
        m = folium.Map(
            location=[(self.start_point[0] + self.end_point[0]) / 2, 
                      (self.start_point[1] + self.end_point[1]) / 2],
            zoom_start=15,
            tiles='OpenStreetMap'
        )
        
        # 添加起点和终点标记
        folium.Marker(
            location=[self.start_point[0], self.start_point[1]],
            popup=f'{direction_name} - 起点',
            icon=folium.Icon(color='green', icon='play')
        ).add_to(m)
        
        folium.Marker(
            location=[self.end_point[0], self.end_point[1]],
            popup=f'{direction_name} - 终点',
            icon=folium.Icon(color='red', icon='stop')
        ).add_to(m)
        
        # 速度颜色映射函数
        def get_speed_color(speed_kmh):
            speed_ranges = [
                (0, 5, '#8B0000'),      # 深红色 0-5 km/h
                (5, 10, '#FF0000'),     # 红色 5-10 km/h
                (10, 15, '#FF4500'),    # 橙红色 10-15 km/h
                (15, 20, '#FFA500'),    # 橙色 15-20 km/h
                (20, 25, '#FFD700'),    # 金色 20-25 km/h
                (25, 30, '#ADFF2F'),    # 绿黄色 25-30 km/h
                (30, 35, '#32CD32'),    # 酸橙绿 30-35 km/h
                (35, 40, '#228B22'),    # 森林绿 35-40 km/h
                (40, 45, '#006400'),    # 深绿色 40-45 km/h
                (45, float('inf'), '#004000')  # 极深绿色 45+ km/h
            ]
            
            for min_speed, max_speed, color in speed_ranges:
                if min_speed <= speed_kmh < max_speed:
                    return color, f"{min_speed}-{max_speed if max_speed != float('inf') else '45+'} km/h"
            return '#808080', '未知'
        
        # 添加路段线条和零速度点
        for segment in analyzer.segments:
            if segment['speed_count'] > 0:
                color, speed_range = get_speed_color(segment['avg_speed'])
                
                # 创建段的线
                folium.PolyLine(
                    locations=[
                        [segment['start'][0], segment['start'][1]],
                        [segment['end'][0], segment['end'][1]]
                    ],
                    color=color,
                    weight=8,
                    opacity=0.8,
                    popup=f"{direction_name} - 段 {segment['id']+1}: 平均速度 {segment['avg_speed']:.2f} km/h<br>"
                          f"速度范围: {speed_range}<br>"
                          f"总数据点: {segment['speed_count']} 个<br>"
                          f"零速度点: {segment['zero_speed_count']} 个"
                ).add_to(m)
                
                # 添加速度为0的点
                for zero_point in segment['zero_speed_points']:
                    folium.CircleMarker(
                        location=[zero_point[0], zero_point[1]],
                        radius=2,
                        color='black',
                        fillColor='red',
                        fillOpacity=0.3,
                        opacity=0.7,
                        popup=f"{direction_name} - 速度为0的点"
                    ).add_to(m)
        
        # 创建图例
        legend_items = []
        legend_items.append(f'<p><b>{direction_name} - 速度图例 (每5km/h一个颜色)</b></p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #8B0000"></i> 0-5 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FF0000"></i> 5-10 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FF4500"></i> 10-15 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FFA500"></i> 15-20 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #FFD700"></i> 20-25 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #ADFF2F"></i> 25-30 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #32CD32"></i> 30-35 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #228B22"></i> 35-40 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #006400"></i> 40-45 km/h</p>')
        legend_items.append('<p><i class="fa fa-square" style="color: #004000"></i> 45+ km/h</p>')
        legend_items.append('<hr>')
        legend_items.append('<p><b>各段数据点数量:</b></p>')
        
        for segment in analyzer.segments:
            if segment['speed_count'] > 0:
                legend_items.append(f'<p>段{segment["id"]+1}: {segment["speed_count"]}点 (零速度:{segment["zero_speed_count"]})</p>')
        
        legend_items.append('<hr>')
        legend_items.append('<p><i class="fa fa-circle" style="color: red; opacity: 0.3"></i> 速度为0的点 (70%透明度)</p>')
        
        legend_html = f'''
        <div style="position: fixed; bottom: 50px; left: 50px; z-index: 1000; background-color: white; 
                    padding: 10px; border: 2px solid grey; border-radius: 5px; max-height: 400px; overflow-y: auto; font-size: 12px;">
            {"".join(legend_items)}
        </div>
        '''
        m.get_root().html.add_child(folium.Element(legend_html))
        
        # 保存地图
        map_path = os.path.join(output_folder, f"{direction_name}_speed_distribution_map.html")
        m.save(map_path)
        print(f"{direction_name}交互式地图已保存至: {map_path}")
        
        return map_path

    def create_segment_chart(self, analyzer, direction_name, output_folder):
        """
        创建各段速度条形图
        """
        plt.figure(figsize=(12, 6))

        segment_ids = [f"段{i+1}" for i in range(len(analyzer.segments))]
        avg_speeds = [segment['avg_speed'] if segment['speed_count'] > 0 else 0 for segment in analyzer.segments]

        # 使用颜色映射函数
        def get_speed_color(speed_kmh):
            if speed_kmh < 5:
                return '#8B0000'
            elif speed_kmh < 10:
                return '#FF0000'
            elif speed_kmh < 15:
                return '#FF4500'
            elif speed_kmh < 20:
                return '#FFA500'
            elif speed_kmh < 25:
                return '#FFD700'
            elif speed_kmh < 30:
                return '#ADFF2F'
            elif speed_kmh < 35:
                return '#32CD32'
            elif speed_kmh < 40:
                return '#228B22'
            elif speed_kmh < 45:
                return '#006400'
            else:
                return '#004000'

        colors = []
        for speed in avg_speeds:
            if speed > 0:
                colors.append(get_speed_color(speed))
            else:
                colors.append('gray')

        bars = plt.bar(segment_ids, avg_speeds, color=colors, alpha=0.8, edgecolor='black')
        plt.xlabel('路段', fontsize=12)
        plt.ylabel('平均速度 (km/h)', fontsize=12)
        plt.title(f'{direction_name} - 各路段平均速度分布', fontsize=14, fontweight='bold')
        plt.xticks(rotation=45)
        plt.grid(axis='y', alpha=0.3)

        # 添加数据点数量标签
        for i, bar in enumerate(bars):
            count = analyzer.segments[i]['speed_count']
            if count > 0:
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                         f"{count}点", ha='center', va='bottom', fontsize=8)

        plt.tight_layout()

        # 保存图表
        chart_path = os.path.join(output_folder, f"{direction_name}_segment_speed_chart.png")
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"{direction_name}速度分布图表已保存至: {chart_path}")

        return chart_path

    def create_interval_chart(self, analyzer, direction_name, output_folder):
        """
        创建速度区间分布柱状图
        """
        print(f"正在为{direction_name}创建速度区间分布图...")

        # 收集所有速度数据点
        all_speeds = []
        for segment in analyzer.segments:
            all_speeds.extend(segment['speeds'])

        if not all_speeds:
            print(f"{direction_name}没有速度数据，跳过速度区间分布图")
            return None

        # 定义速度区间
        max_speed = max(all_speeds)
        intervals = []
        interval_counts = []
        interval_labels = []

        current_speed = 0
        while current_speed <= max_speed + 5:
            next_speed = current_speed + 5

            count = sum(1 for speed in all_speeds if current_speed <= speed < next_speed)

            intervals.append((current_speed, next_speed))
            interval_counts.append(count)

            if next_speed > max_speed + 5:
                interval_labels.append(f"{current_speed}+ km/h")
            else:
                interval_labels.append(f"{current_speed}-{next_speed} km/h")

            current_speed = next_speed

            if current_speed > 100:
                break

        # 创建柱状图
        plt.figure(figsize=(12, 8))

        colors = ['#8B0000', '#FF0000', '#FF4500', '#FFA500', '#FFD700',
                 '#ADFF2F', '#32CD32', '#228B22', '#006400', '#004000']

        while len(colors) < len(interval_counts):
            colors.append('#004000')

        bars = plt.bar(range(len(interval_counts)), interval_counts,
                      color=colors[:len(interval_counts)], alpha=0.8, edgecolor='black')

        plt.xlabel('速度区间 (km/h)', fontsize=12)
        plt.ylabel('数据点数量', fontsize=12)
        plt.title(f'{direction_name} - 速度区间分布统计\n(每5km/h一个区间)', fontsize=14, fontweight='bold')
        plt.xticks(range(len(interval_labels)), interval_labels, rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)

        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            if height > 0:
                plt.text(bar.get_x() + bar.get_width()/2, height + max(interval_counts)*0.01,
                        f'{int(height)}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图表
        interval_chart_path = os.path.join(output_folder, f"{direction_name}_speed_interval_distribution.png")
        plt.savefig(interval_chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"{direction_name}速度区间分布图已保存至: {interval_chart_path}")

        # 输出统计信息
        print(f"\n{direction_name}速度区间统计:")
        total_points = sum(interval_counts)
        for i, (interval, count) in enumerate(zip(intervals, interval_counts)):
            if count > 0:
                percentage = (count / total_points) * 100
                print(f"{interval_labels[i]}: {count} 个点 ({percentage:.1f}%)")

        return interval_chart_path

    def export_detailed_data(self, analyzer, direction_name, output_folder):
        """
        导出详细数据到CSV
        """
        results_df = pd.DataFrame({
            '段ID': [i+1 for i in range(len(analyzer.segments))],
            '起点纬度': [segment['start'][0] for segment in analyzer.segments],
            '起点经度': [segment['start'][1] for segment in analyzer.segments],
            '终点纬度': [segment['end'][0] for segment in analyzer.segments],
            '终点经度': [segment['end'][1] for segment in analyzer.segments],
            '平均速度(km/h)': [segment['avg_speed'] if segment['speed_count'] > 0 else 0 for segment in analyzer.segments],
            '数据点数量': [segment['speed_count'] for segment in analyzer.segments],
            '零速度点数量': [segment['zero_speed_count'] for segment in analyzer.segments]
        })

        csv_path = os.path.join(output_folder, f"{direction_name}_speed_distribution_data.csv")
        results_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"{direction_name}分析数据已保存至: {csv_path}")

        return csv_path

    def create_comparison_report(self, output_base_folder):
        """
        创建对比分析报告
        """
        if not self.upward_results or not self.downward_results:
            print("缺少分析结果，无法生成对比报告")
            return None

        upward_analyzer = self.upward_results['analyzer']
        downward_analyzer = self.downward_results['analyzer']

        # 收集统计数据
        upward_stats = self.collect_statistics(upward_analyzer, "上行")
        downward_stats = self.collect_statistics(downward_analyzer, "下行")

        # 生成报告内容
        report_content = self.generate_report_content(upward_stats, downward_stats)

        # 保存报告
        report_path = os.path.join(output_base_folder, "上下行对比分析报告.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"对比分析报告已保存至: {report_path}")
        return report_path

    def collect_statistics(self, analyzer, direction_name):
        """
        收集统计数据
        """
        # 收集所有速度数据
        all_speeds = []
        total_points = 0
        zero_speed_points = 0

        for segment in analyzer.segments:
            all_speeds.extend(segment['speeds'])
            total_points += segment['speed_count']
            zero_speed_points += segment['zero_speed_count']

        # 计算速度区间分布
        speed_intervals = {}
        interval_ranges = [(0, 5), (5, 10), (10, 15), (15, 20), (20, 25),
                          (25, 30), (30, 35), (35, 40), (40, 45), (45, float('inf'))]

        for min_speed, max_speed in interval_ranges:
            if max_speed == float('inf'):
                count = sum(1 for speed in all_speeds if speed >= min_speed)
                key = f"{min_speed}+ km/h"
            else:
                count = sum(1 for speed in all_speeds if min_speed <= speed < max_speed)
                key = f"{min_speed}-{max_speed} km/h"

            percentage = (count / total_points * 100) if total_points > 0 else 0
            speed_intervals[key] = {'count': count, 'percentage': percentage}

        # 找出最快和最慢的段
        valid_segments = [s for s in analyzer.segments if s['speed_count'] > 0]
        if valid_segments:
            fastest_segment = max(valid_segments, key=lambda x: x['avg_speed'])
            slowest_segment = min(valid_segments, key=lambda x: x['avg_speed'])
            avg_speeds = [s['avg_speed'] for s in valid_segments]
            speed_range = (min(avg_speeds), max(avg_speeds))
            overall_avg = np.mean(avg_speeds)
        else:
            fastest_segment = slowest_segment = None
            speed_range = (0, 0)
            overall_avg = 0

        return {
            'direction': direction_name,
            'total_points': total_points,
            'zero_speed_points': zero_speed_points,
            'zero_speed_percentage': (zero_speed_points / total_points * 100) if total_points > 0 else 0,
            'speed_intervals': speed_intervals,
            'speed_range': speed_range,
            'overall_avg': overall_avg,
            'fastest_segment': fastest_segment,
            'slowest_segment': slowest_segment,
            'valid_segments_count': len(valid_segments)
        }

    def generate_report_content(self, upward_stats, downward_stats):
        """
        生成对比报告内容
        """
        report = f"""# 上下行公交车速度分布对比分析报告

## 分析概述

本报告对比分析了同一路段上行和下行公交车的速度分布特征，路段起终点为：
- **起点**: {self.start_point}
- **终点**: {self.end_point}
- **路段长度**: {geodesic(self.start_point, self.end_point).meters:.1f}米
- **分析方法**: 30米分段，共18段

## 整体对比统计

| 指标 | 上行数据 | 下行数据 | 差异 |
|------|----------|----------|------|
| 总数据点 | {upward_stats['total_points']:,}个 | {downward_stats['total_points']:,}个 | 下行{'多' if downward_stats['total_points'] > upward_stats['total_points'] else '少'}{abs(downward_stats['total_points'] - upward_stats['total_points']) / upward_stats['total_points'] * 100:.1f}% |
| 零速度点 | {upward_stats['zero_speed_points']:,}个 ({upward_stats['zero_speed_percentage']:.1f}%) | {downward_stats['zero_speed_points']:,}个 ({downward_stats['zero_speed_percentage']:.1f}%) | {'上行' if upward_stats['zero_speed_percentage'] > downward_stats['zero_speed_percentage'] else '下行'}停车更频繁 |
| 平均速度范围 | {upward_stats['speed_range'][0]:.2f}-{upward_stats['speed_range'][1]:.2f} km/h | {downward_stats['speed_range'][0]:.2f}-{downward_stats['speed_range'][1]:.2f} km/h | 下行{'略高' if downward_stats['speed_range'][1] > upward_stats['speed_range'][1] else '略低'} |
| 整体平均速度 | {upward_stats['overall_avg']:.2f} km/h | {downward_stats['overall_avg']:.2f} km/h | 下行{'快' if downward_stats['overall_avg'] > upward_stats['overall_avg'] else '慢'}{abs(downward_stats['overall_avg'] - upward_stats['overall_avg']) / upward_stats['overall_avg'] * 100:.1f}% |

## 速度区间分布对比

| 速度区间 | 上行数据 | 下行数据 | 差异分析 |
|----------|----------|----------|----------|"""

        # 添加速度区间对比
        for interval in ['0-5 km/h', '5-10 km/h', '10-15 km/h', '15-20 km/h', '20-25 km/h',
                        '25-30 km/h', '30-35 km/h', '35-40 km/h', '40-45 km/h', '45+ km/h']:
            up_data = upward_stats['speed_intervals'].get(interval, {'count': 0, 'percentage': 0})
            down_data = downward_stats['speed_intervals'].get(interval, {'count': 0, 'percentage': 0})

            if up_data['percentage'] > down_data['percentage']:
                diff_analysis = "上行更多"
            elif down_data['percentage'] > up_data['percentage']:
                diff_analysis = "下行更多"
            else:
                diff_analysis = "基本相当"

            report += f"\n| {interval} | {up_data['count']:,} ({up_data['percentage']:.1f}%) | {down_data['count']:,} ({down_data['percentage']:.1f}%) | {diff_analysis} |"

        # 添加关键发现
        report += f"""

## 关键发现

### 1. 速度分布差异
- **整体速度**: {'下行' if downward_stats['overall_avg'] > upward_stats['overall_avg'] else '上行'}整体更快，平均速度高{abs(downward_stats['overall_avg'] - upward_stats['overall_avg']):.2f} km/h
- **停车频率**: {'上行' if upward_stats['zero_speed_percentage'] > downward_stats['zero_speed_percentage'] else '下行'}停车更频繁，零速度点占比高{abs(upward_stats['zero_speed_percentage'] - downward_stats['zero_speed_percentage']):.1f}%

### 2. 拥堵点分析
- **上行最慢段**: 段{upward_stats['slowest_segment']['id']+1 if upward_stats['slowest_segment'] else 'N/A'} ({upward_stats['slowest_segment']['avg_speed']:.2f} km/h, {upward_stats['slowest_segment']['speed_count']}个数据点)
- **下行最慢段**: 段{downward_stats['slowest_segment']['id']+1 if downward_stats['slowest_segment'] else 'N/A'} ({downward_stats['slowest_segment']['avg_speed']:.2f} km/h, {downward_stats['slowest_segment']['speed_count']}个数据点)

### 3. 高速段分析
- **上行最快段**: 段{upward_stats['fastest_segment']['id']+1 if upward_stats['fastest_segment'] else 'N/A'} ({upward_stats['fastest_segment']['avg_speed']:.2f} km/h)
- **下行最快段**: 段{downward_stats['fastest_segment']['id']+1 if downward_stats['fastest_segment'] else 'N/A'} ({downward_stats['fastest_segment']['avg_speed']:.2f} km/h)

## 生成文件

### 上行数据文件
- 交互式地图: `{self.upward_results['map_path'] if self.upward_results else 'N/A'}`
- 各段速度图: `{self.upward_results['chart_path'] if self.upward_results else 'N/A'}`
- 速度区间分布图: `{self.upward_results['interval_chart_path'] if self.upward_results else 'N/A'}`
- 详细数据: `{self.upward_results['csv_path'] if self.upward_results else 'N/A'}`

### 下行数据文件
- 交互式地图: `{self.downward_results['map_path'] if self.downward_results else 'N/A'}`
- 各段速度图: `{self.downward_results['chart_path'] if self.downward_results else 'N/A'}`
- 速度区间分布图: `{self.downward_results['interval_chart_path'] if self.downward_results else 'N/A'}`
- 详细数据: `{self.downward_results['csv_path'] if self.downward_results else 'N/A'}`

## 建议

1. **针对性改善**: 重点关注两个方向共同的低速段
2. **信号优化**: 考虑为速度较慢的方向提供更好的信号配时
3. **深入分析**: 建议进行时间维度的进一步分析
4. **实地调研**: 对关键拥堵段进行实地调研

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return report

    def run_complete_analysis(self):
        """
        运行完整的双向对比分析
        """
        print("开始双向速度分布对比分析...")
        print(f"起点: {self.start_point}")
        print(f"终点: {self.end_point}")
        print(f"上行数据文件夹: {self.upward_folder}")
        print(f"下行数据文件夹: {self.downward_folder}")

        # 创建输出文件夹
        output_base_folder = "dual_direction_analysis_results"
        if not os.path.exists(output_base_folder):
            os.makedirs(output_base_folder)

        # 分析上行数据
        upward_analyzer, upward_files = self.analyze_single_direction(self.upward_folder, "上行")
        if upward_analyzer:
            self.upward_results = self.create_direction_outputs(upward_analyzer, "上行", output_base_folder)

        # 分析下行数据
        downward_analyzer, downward_files = self.analyze_single_direction(self.downward_folder, "下行")
        if downward_analyzer:
            self.downward_results = self.create_direction_outputs(downward_analyzer, "下行", output_base_folder)

        # 生成对比报告
        if self.upward_results and self.downward_results:
            report_path = self.create_comparison_report(output_base_folder)

            print(f"\n{'='*80}")
            print("双向对比分析完成！")
            print(f"{'='*80}")

            # 输出摘要
            upward_stats = self.collect_statistics(self.upward_results['analyzer'], "上行")
            downward_stats = self.collect_statistics(self.downward_results['analyzer'], "下行")

            print(f"\n上行数据摘要:")
            print(f"- 总数据点: {upward_stats['total_points']:,}个")
            print(f"- 平均速度: {upward_stats['overall_avg']:.2f} km/h")
            print(f"- 零速度点占比: {upward_stats['zero_speed_percentage']:.1f}%")

            print(f"\n下行数据摘要:")
            print(f"- 总数据点: {downward_stats['total_points']:,}个")
            print(f"- 平均速度: {downward_stats['overall_avg']:.2f} km/h")
            print(f"- 零速度点占比: {downward_stats['zero_speed_percentage']:.1f}%")

            print(f"\n生成的文件:")
            print(f"- 上行结果文件夹: {output_base_folder}/上行_results/")
            print(f"- 下行结果文件夹: {output_base_folder}/下行_results/")
            print(f"- 对比分析报告: {report_path}")

            return True
        else:
            print("分析失败，请检查数据文件夹和参数设置。")
            return False


def main():
    """
    主函数 - 执行双向速度分布对比分析
    """
    # 定义起终点坐标
    start_point = (29.585958, 106.314104)  # (纬度, 经度)
    end_point = (29.590679, 106.314488)    # (纬度, 经度)

    # 数据文件夹路径
    upward_folder = "上行数据"
    downward_folder = "下行数据"

    # 创建双向分析器实例
    analyzer = DualDirectionSpeedAnalyzer(start_point, end_point, upward_folder, downward_folder)

    # 运行完整分析
    success = analyzer.run_complete_analysis()

    if success:
        print("\n✅ 双向对比分析成功完成！")
        print("请查看生成的文件以获取详细结果。")
    else:
        print("\n❌ 分析失败，请检查数据和参数设置。")


if __name__ == "__main__":
    main()
