# 公交车速度分布分析工具使用说明

## 工具概述

本工具包含两个主要脚本，用于分析公交车在指定路段的速度分布：

1. **speed_distribution_analysis.py** - 单向分析脚本
2. **dual_direction_speed_analysis.py** - 双向对比分析脚本（推荐使用）

## 功能特点

### ✅ 已实现的优化功能
- **每5km/h颜色编码**：10个颜色等级，从深红色(0-5km/h)到极深绿色(45+km/h)
- **30米精细分段**：从原来的50米优化到30米，提供更精细的空间分辨率
- **图例显示数据点数量**：在图例中显示每段的总数据点数量和零速度点数量
- **70%透明度零速度点**：在地图上用红色小圆点标记所有速度为0的位置
- **速度区间分布图**：柱状图显示各5km/h速度区间的数据点统计

## 生成的文件

### 单向分析（每个方向4个文件）
1. **交互式地图** (`*_speed_distribution_map.html`)
   - 显示30米分段的速度分布
   - 每5km/h颜色编码
   - 70%透明度标记零速度点
   - 详细图例包含数据点统计

2. **各段速度图** (`*_segment_speed_chart.png`)
   - 条形图显示18个路段的平均速度
   - 与地图相同的颜色编码
   - 显示每段的数据点数量

3. **速度区间分布图** (`*_speed_interval_distribution.png`)
   - 柱状图显示各速度区间的数据点统计
   - 横坐标：速度区间（每5km/h）
   - 纵坐标：该区间内的数据点数量

4. **详细数据CSV** (`*_speed_distribution_data.csv`)
   - 包含18段的详细分析数据
   - 坐标、平均速度、数据点数量、零速度点数量

### 双向对比分析（总计9个文件）
- **上行结果文件夹**：包含上行数据的4个文件
- **下行结果文件夹**：包含下行数据的4个文件
- **对比分析报告**：详细的上下行对比分析报告

## 使用方法

### 方法1：双向对比分析（推荐）
```bash
python dual_direction_speed_analysis.py
```

### 方法2：单向分析
```bash
# 分析上行数据
python speed_distribution_analysis.py 上行数据

# 分析下行数据
python speed_distribution_analysis.py 下行数据
```

## 输出文件结构

```
dual_direction_analysis_results/
├── 上下行对比分析报告.md
├── 上行_results/
│   ├── 上行_speed_distribution_map.html
│   ├── 上行_segment_speed_chart.png
│   ├── 上行_speed_interval_distribution.png
│   └── 上行_speed_distribution_data.csv
└── 下行_results/
    ├── 下行_speed_distribution_map.html
    ├── 下行_segment_speed_chart.png
    ├── 下行_speed_interval_distribution.png
    └── 下行_speed_distribution_data.csv
```

## 分析参数

- **起点坐标**: (29.585958, 106.314104)
- **终点坐标**: (29.590679, 106.314488)
- **路段总长度**: 524.6米
- **识别半径**: 20米（用于筛选经过起终点的公交车）
- **走廊宽度**: 30米（左右各15米）
- **分段长度**: 30米
- **总段数**: 18段

## 主要发现

### 上行 vs 下行对比
- **下行整体更快**：平均速度17.43 km/h vs 15.75 km/h（快10.7%）
- **上行停车更频繁**：零速度点占比51.6% vs 45.3%
- **共同瓶颈点**：段11是两个方向都最慢的路段
- **数据量差异**：下行数据点比上行多34.9%

### 速度分布特征
- **低速占主导**：两个方向都有大量0-5km/h的数据点
- **高速点稀少**：40km/h以上的高速点很少
- **空间变化明显**：不同路段间速度差异显著

## 技术说明

### 数据处理逻辑
1. **数据筛选**：识别经过起终点20米范围内的公交车
2. **路线走廊**：创建30米宽的狭长多边形区域
3. **分段计算**：将路线分为18个30米长的段
4. **速度计算**：计算每段内GPS点的平均速度
5. **可视化**：生成多种图表和交互式地图

### 零速度点说明
- **正常现象**：公交车在红绿灯、站点、拥堵时频繁停车
- **数据真实性**：通过检查原始数据验证了零速度点的真实性
- **合理比例**：城市公交45-52%的零速度点占比是合理的

## 建议用途

1. **交通规划**：识别拥堵瓶颈点，优化交通信号配时
2. **路线优化**：分析不同路段的通行效率
3. **对比研究**：比较上下行方向的交通特征差异
4. **时间分析**：可扩展为不同时段的速度分布分析

## 注意事项

- 确保数据文件夹中包含正确格式的GPS轨迹数据
- 交互式地图需要在浏览器中打开查看
- CSV文件可用于进一步的数据分析和处理
- 建议结合实地调研验证分析结果
