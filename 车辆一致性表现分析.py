import os
import csv
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.font_manager as fm
from datetime import datetime, time
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体"""
    chinese_fonts = [
        'Microsoft YaHei',
        'SimHei', 
        'SimSun',
        'KaiTi',
        'FangSong'
    ]
    
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font]
        plt.rcParams['axes.unicode_minus'] = False
        print(f"使用字体: {selected_font}")
        return True
    else:
        print("警告：未找到合适的中文字体，将使用英文标签")
        return False

def get_time_period(start_time):
    """根据起始时间判断时段"""
    t = start_time.time()

    if time(6, 0) <= t < time(7, 30):
        return "06:00-07:30"
    elif time(7, 30) <= t < time(9, 0):
        return "07:30-09:00"
    elif time(9, 0) <= t < time(10, 30):
        return "09:00-10:30"
    elif time(10, 30) <= t < time(12, 0):
        return "10:30-12:00"
    elif time(12, 0) <= t < time(13, 30):
        return "12:00-13:30"
    elif time(13, 30) <= t < time(15, 0):
        return "13:30-15:00"
    elif time(15, 0) <= t < time(16, 30):
        return "15:00-16:30"
    elif time(16, 30) <= t < time(18, 0):
        return "16:30-18:00"
    elif time(18, 0) <= t < time(19, 30):
        return "18:00-19:30"
    elif time(19, 30) <= t < time(21, 0):
        return "19:30-21:00"
    return None

def analyze_vehicle_consistency_at_intersection(intersection_folder):
    """分析单个交叉口的车辆一致性表现"""
    intersection_name = os.path.basename(intersection_folder)
    print(f"正在分析交叉口: {intersection_name}")
    
    # 存储每个车辆在每个时段的表现数据
    vehicle_performance = defaultdict(lambda: defaultdict(list))
    
    # 获取该交叉口下的所有车辆文件
    vehicle_files = [f for f in os.listdir(intersection_folder) if f.endswith('.txt')]
    
    for vehicle_file in vehicle_files:
        vehicle_path = os.path.join(intersection_folder, vehicle_file)
        car_num = vehicle_file.replace('CarNum_', '').replace('.txt', '')
        
        # 读取车辆数据
        data = []
        with open(vehicle_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过标题行
            for row in reader:
                if len(row) < 5:
                    continue
                try:
                    gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                    speed = float(row[4])
                    data.append((gps_time, speed))
                except ValueError:
                    continue
        
        if not data:
            continue
            
        # 按时间排序
        data.sort(key=lambda x: x[0])
        
        # 按日期分组分析
        daily_data = defaultdict(list)
        for gps_time, speed in data:
            date_key = gps_time.date()
            daily_data[date_key].append((gps_time, speed))
        
        # 分析每天的数据
        for date_key, day_data in daily_data.items():
            # 按时段分析该车该天的通过情况
            time_period_passages = defaultdict(list)
            
            for gps_time, speed in day_data:
                time_period = get_time_period(gps_time)
                if time_period:
                    time_period_passages[time_period].append((gps_time, speed))
            
            # 分析每个时段的表现
            for time_period, passage_data in time_period_passages.items():
                if not passage_data:
                    continue
                
                # 计算该车辆在该时段该天的表现指标
                total_stop_time = 0.0
                stop_events = 0
                max_stop_time = 0.0
                min_speed = min(speed for _, speed in passage_data)
                avg_speed = sum(speed for _, speed in passage_data) / len(passage_data)
                
                i = 0
                while i < len(passage_data):
                    gps_time, speed = passage_data[i]
                    
                    # 检查是否停车（速度<=5km/h）
                    if speed <= 5.0:
                        # 找到停车事件的开始和结束
                        stop_start_time = gps_time
                        stop_end_time = gps_time
                        j = i
                        
                        # 向后查找停车结束时间
                        while j < len(passage_data) and passage_data[j][1] <= 5.0:
                            stop_end_time = passage_data[j][0]
                            j += 1
                        
                        # 计算停车时间
                        stop_duration = (stop_end_time - stop_start_time).total_seconds()
                        
                        # 如果停车时间超过1秒，记录停车
                        if stop_duration >= 1:
                            stop_events += 1
                            total_stop_time += stop_duration
                            max_stop_time = max(max_stop_time, stop_duration)
                        
                        i = j  # 跳过已处理的停车时间段
                    else:
                        i += 1
                
                # 记录该车辆在该时段的表现
                performance = {
                    'car_num': car_num,
                    'date': date_key,
                    'has_stop': stop_events > 0,
                    'stop_events': stop_events,
                    'total_stop_time': total_stop_time,
                    'max_stop_time': max_stop_time,
                    'avg_stop_time': total_stop_time / stop_events if stop_events > 0 else 0,
                    'min_speed': min_speed,
                    'avg_speed': avg_speed,
                    'passage_duration': (passage_data[-1][0] - passage_data[0][0]).total_seconds()
                }
                
                vehicle_performance[time_period][car_num].append(performance)
    
    return intersection_name, dict(vehicle_performance)

def calculate_consistency_metrics(vehicle_performance):
    """计算一致性指标"""
    consistency_results = []
    
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00", 
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00", 
                         "18:00-19:30", "19:30-21:00"]
    
    for intersection_name, time_period_data in vehicle_performance.items():
        for time_period in time_periods_order:
            if time_period not in time_period_data:
                continue
                
            period_data = time_period_data[time_period]
            
            # 收集所有车辆的表现数据
            all_stop_rates = []
            all_avg_stop_times = []
            all_max_stop_times = []
            all_avg_speeds = []
            
            vehicle_stats = {}
            
            for car_num, performances in period_data.items():
                if len(performances) < 3:  # 至少需要3天的数据才有统计意义
                    continue
                
                # 计算该车辆的平均表现
                stop_rate = sum(1 for p in performances if p['has_stop']) / len(performances) * 100
                avg_stop_time = np.mean([p['avg_stop_time'] for p in performances if p['avg_stop_time'] > 0])
                max_stop_time = max([p['max_stop_time'] for p in performances])
                avg_speed = np.mean([p['avg_speed'] for p in performances])
                
                if not np.isnan(avg_stop_time):
                    all_stop_rates.append(stop_rate)
                    all_avg_stop_times.append(avg_stop_time)
                    all_max_stop_times.append(max_stop_time)
                    all_avg_speeds.append(avg_speed)
                    
                    vehicle_stats[car_num] = {
                        'stop_rate': stop_rate,
                        'avg_stop_time': avg_stop_time,
                        'max_stop_time': max_stop_time,
                        'avg_speed': avg_speed,
                        'sample_count': len(performances)
                    }
            
            if len(all_stop_rates) < 2:  # 至少需要2辆车才能比较
                continue
            
            # 计算一致性指标（变异系数 = 标准差/均值）
            stop_rate_cv = np.std(all_stop_rates) / np.mean(all_stop_rates) if np.mean(all_stop_rates) > 0 else 0
            avg_stop_time_cv = np.std(all_avg_stop_times) / np.mean(all_avg_stop_times) if np.mean(all_avg_stop_times) > 0 else 0
            max_stop_time_cv = np.std(all_max_stop_times) / np.mean(all_max_stop_times) if np.mean(all_max_stop_times) > 0 else 0
            avg_speed_cv = np.std(all_avg_speeds) / np.mean(all_avg_speeds) if np.mean(all_avg_speeds) > 0 else 0
            
            # 综合一致性评分（变异系数越小，一致性越好）
            consistency_score = 1 / (1 + (stop_rate_cv + avg_stop_time_cv + max_stop_time_cv + avg_speed_cv) / 4)
            
            consistency_results.append({
                '交叉口': intersection_name,
                '时段': time_period,
                '车辆数量': len(vehicle_stats),
                '停车率变异系数': round(stop_rate_cv, 4),
                '平均停留时间变异系数': round(avg_stop_time_cv, 4),
                '最长停留时间变异系数': round(max_stop_time_cv, 4),
                '平均速度变异系数': round(avg_speed_cv, 4),
                '一致性评分': round(consistency_score, 4),
                '平均停车率(%)': round(np.mean(all_stop_rates), 2),
                '平均停留时间(秒)': round(np.mean(all_avg_stop_times), 2),
                '最长停留时间(秒)': round(np.mean(all_max_stop_times), 2),
                '平均速度(km/h)': round(np.mean(all_avg_speeds), 2),
                '车辆详细数据': vehicle_stats
            })
    
    return consistency_results

def main():
    """主分析流程"""
    # 下行数据目录
    intersections_dir = '按交叉口分组的数据_下行'
    
    if not os.path.exists(intersections_dir):
        print(f"错误：找不到目录 {intersections_dir}")
        return
    
    # 获取所有交叉口文件夹
    intersection_folders = [f for f in os.listdir(intersections_dir) 
                           if os.path.isdir(os.path.join(intersections_dir, f))]
    
    print(f"找到 {len(intersection_folders)} 个交叉口")
    
    # 存储所有分析结果
    all_vehicle_performance = {}
    
    # 分析每个交叉口
    for intersection_folder in intersection_folders:
        intersection_path = os.path.join(intersections_dir, intersection_folder)
        intersection_name, vehicle_performance = analyze_vehicle_consistency_at_intersection(intersection_path)
        all_vehicle_performance[intersection_name] = vehicle_performance
    
    # 计算一致性指标
    print("\n正在计算一致性指标...")
    consistency_results = calculate_consistency_metrics(all_vehicle_performance)
    
    # 保存结果
    df_consistency = pd.DataFrame([{k: v for k, v in result.items() if k != '车辆详细数据'} 
                                  for result in consistency_results])
    df_consistency = df_consistency.sort_values(['交叉口', '时段'])
    df_consistency.to_csv('车辆一致性表现分析结果.csv', index=False, encoding='utf_8_sig')
    print(f"\n分析结果已保存到: 车辆一致性表现分析结果.csv")
    
    # 生成分析报告
    print("\n=== 车辆一致性表现分析报告 ===")
    
    print("\n1. 一致性最好的交叉口（按时段）:")
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00", 
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00", 
                         "18:00-19:30", "19:30-21:00"]
    
    for time_period in time_periods_order:
        period_data = df_consistency[df_consistency['时段'] == time_period]
        if not period_data.empty:
            best_consistency = period_data.loc[period_data['一致性评分'].idxmax()]
            print(f"  {time_period}: {best_consistency['交叉口']} (评分: {best_consistency['一致性评分']:.4f})")
    
    print("\n2. 整体一致性最好的交叉口:")
    intersection_consistency = df_consistency.groupby('交叉口')['一致性评分'].mean().sort_values(ascending=False)
    for i, (intersection, score) in enumerate(intersection_consistency.head(5).items(), 1):
        print(f"  {i}. {intersection}: {score:.4f}")
    
    print("\n3. 一致性最差的交叉口:")
    for i, (intersection, score) in enumerate(intersection_consistency.tail(5).items(), 1):
        print(f"  {i}. {intersection}: {score:.4f}")
    
    # 创建可视化
    create_consistency_visualizations(df_consistency)
    
    print("\n分析完成！")

def create_consistency_visualizations(df_consistency):
    """创建一致性分析可视化"""
    has_chinese_font = setup_chinese_font()
    
    # 1. 一致性评分热力图
    plt.figure(figsize=(16, 12))
    pivot_consistency = df_consistency.pivot(index='交叉口', columns='时段', values='一致性评分')
    
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00", 
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00", 
                         "18:00-19:30", "19:30-21:00"]
    pivot_consistency = pivot_consistency.reindex(columns=time_periods_order)
    
    if has_chinese_font:
        sns.heatmap(pivot_consistency, annot=True, fmt='.3f', cmap='RdYlGn', 
                    cbar_kws={'label': '一致性评分'}, linewidths=0.5)
        plt.title('各交叉口分时段车辆表现一致性评分热力图', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('时段', fontsize=12)
        plt.ylabel('交叉口', fontsize=12)
    else:
        sns.heatmap(pivot_consistency, annot=True, fmt='.3f', cmap='RdYlGn', 
                    cbar_kws={'label': 'Consistency Score'}, linewidths=0.5)
        plt.title('Vehicle Performance Consistency Score Heatmap', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('Time Period', fontsize=12)
        plt.ylabel('Intersection', fontsize=12)
    
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('车辆一致性评分热力图.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("一致性分析可视化图表已生成并保存！")

if __name__ == '__main__':
    main()
