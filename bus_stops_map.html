<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_7a2bae93d67e65a9df98689030436e48 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
    <div style="position: fixed; 
                top: 10px; right: 10px; width: 150px; height: 90px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; padding: 10px">
    <p><b>公交站点图例</b></p>
    <p><i class="fa fa-arrow-down" style="color:blue"></i> 下行站点</p>
    <p><i class="fa fa-arrow-up" style="color:red"></i> 上行站点</p>
    </div>
    
    
            <div class="folium-map" id="map_7a2bae93d67e65a9df98689030436e48" ></div>
        
</body>
<script>
    
    
            var map_7a2bae93d67e65a9df98689030436e48 = L.map(
                "map_7a2bae93d67e65a9df98689030436e48",
                {
                    center: [29.5920609, 106.31754836666667],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 13,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_1978e0f6f96a187986fd4a3649a9af40 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_1978e0f6f96a187986fd4a3649a9af40.addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var marker_9eb4dadb589ce7cf413d7153121db2b6 = L.marker(
                [29.617029, 106.339491],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_ae094e8182e08c619f72a08c60ee50df = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_e00806bf323bcab508f65366cf6b7281 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_de537adb5561315afcf1cd1d1b23d68b = $(`<div id="html_de537adb5561315afcf1cd1d1b23d68b" style="width: 100.0%; height: 100.0%;">陈家桥新医院<br>方向: 下行<br>序号: 1</div>`)[0];
                popup_e00806bf323bcab508f65366cf6b7281.setContent(html_de537adb5561315afcf1cd1d1b23d68b);
            
        

        marker_9eb4dadb589ce7cf413d7153121db2b6.bindPopup(popup_e00806bf323bcab508f65366cf6b7281)
        ;

        
    
    
            marker_9eb4dadb589ce7cf413d7153121db2b6.bindTooltip(
                `<div>
                     陈家桥新医院 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_9eb4dadb589ce7cf413d7153121db2b6.setIcon(icon_ae094e8182e08c619f72a08c60ee50df);
            
    
            var marker_62e438b3d221c38e690656daf1c857ac = L.marker(
                [29.611608, 106.341308],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_b73c9490c87c337be63ca55003ebead3 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_9506d8be7c76570b9885eedad95d10ad = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_63c59cf76c7ab8d1aac4845041b10e65 = $(`<div id="html_63c59cf76c7ab8d1aac4845041b10e65" style="width: 100.0%; height: 100.0%;">陈南路口<br>方向: 下行<br>序号: 2</div>`)[0];
                popup_9506d8be7c76570b9885eedad95d10ad.setContent(html_63c59cf76c7ab8d1aac4845041b10e65);
            
        

        marker_62e438b3d221c38e690656daf1c857ac.bindPopup(popup_9506d8be7c76570b9885eedad95d10ad)
        ;

        
    
    
            marker_62e438b3d221c38e690656daf1c857ac.bindTooltip(
                `<div>
                     陈南路口 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_62e438b3d221c38e690656daf1c857ac.setIcon(icon_b73c9490c87c337be63ca55003ebead3);
            
    
            var marker_707b88c292cea0574ed973a52da65638 = L.marker(
                [29.610001, 106.33535],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_6ee4d02ac8c1a78e75d1164f7e6e4e3c = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_0dfd929e3eb34d536d505f1d5dd94e0c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_46b708f9e172ed96c2f091026122dfa6 = $(`<div id="html_46b708f9e172ed96c2f091026122dfa6" style="width: 100.0%; height: 100.0%;">大学城北路东段路口<br>方向: 下行<br>序号: 3</div>`)[0];
                popup_0dfd929e3eb34d536d505f1d5dd94e0c.setContent(html_46b708f9e172ed96c2f091026122dfa6);
            
        

        marker_707b88c292cea0574ed973a52da65638.bindPopup(popup_0dfd929e3eb34d536d505f1d5dd94e0c)
        ;

        
    
    
            marker_707b88c292cea0574ed973a52da65638.bindTooltip(
                `<div>
                     大学城北路东段路口 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_707b88c292cea0574ed973a52da65638.setIcon(icon_6ee4d02ac8c1a78e75d1164f7e6e4e3c);
            
    
            var marker_8fe5f02b16870785fcc0f5752c407987 = L.marker(
                [29.610164, 106.324727],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_8bb90083307f21837a1a20835fb8ff46 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_09674c5fa6275b5d6977a2fa0c045e8c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c83d59e8f4b6fe0bfc23c21afb98a1d8 = $(`<div id="html_c83d59e8f4b6fe0bfc23c21afb98a1d8" style="width: 100.0%; height: 100.0%;">轨道陈家桥站<br>方向: 下行<br>序号: 4</div>`)[0];
                popup_09674c5fa6275b5d6977a2fa0c045e8c.setContent(html_c83d59e8f4b6fe0bfc23c21afb98a1d8);
            
        

        marker_8fe5f02b16870785fcc0f5752c407987.bindPopup(popup_09674c5fa6275b5d6977a2fa0c045e8c)
        ;

        
    
    
            marker_8fe5f02b16870785fcc0f5752c407987.bindTooltip(
                `<div>
                     轨道陈家桥站 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_8fe5f02b16870785fcc0f5752c407987.setIcon(icon_8bb90083307f21837a1a20835fb8ff46);
            
    
            var marker_8f122d08356b159c98efc4b5b5de48e8 = L.marker(
                [29.608722, 106.316293],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_460236f16726e15351410d9908180bd0 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_e8cb99553513bdbe7fc8ea303a28dc52 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e4c56945ccae35965cdcc5c6a7e108f1 = $(`<div id="html_e4c56945ccae35965cdcc5c6a7e108f1" style="width: 100.0%; height: 100.0%;">大学城东路东<br>方向: 下行<br>序号: 5</div>`)[0];
                popup_e8cb99553513bdbe7fc8ea303a28dc52.setContent(html_e4c56945ccae35965cdcc5c6a7e108f1);
            
        

        marker_8f122d08356b159c98efc4b5b5de48e8.bindPopup(popup_e8cb99553513bdbe7fc8ea303a28dc52)
        ;

        
    
    
            marker_8f122d08356b159c98efc4b5b5de48e8.bindTooltip(
                `<div>
                     大学城东路东 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_8f122d08356b159c98efc4b5b5de48e8.setIcon(icon_460236f16726e15351410d9908180bd0);
            
    
            var marker_a0b18219a46e347cccf911129796f3ed = L.marker(
                [29.602679, 106.315368],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_9d8bb31633482add0702cb5472f73e00 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_d707e73dfbe4f19a871088c1dfe793c1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_93c0f7b32f70d77d1bb8a6d4d0c1f89a = $(`<div id="html_93c0f7b32f70d77d1bb8a6d4d0c1f89a" style="width: 100.0%; height: 100.0%;">大学城东路<br>方向: 下行<br>序号: 6</div>`)[0];
                popup_d707e73dfbe4f19a871088c1dfe793c1.setContent(html_93c0f7b32f70d77d1bb8a6d4d0c1f89a);
            
        

        marker_a0b18219a46e347cccf911129796f3ed.bindPopup(popup_d707e73dfbe4f19a871088c1dfe793c1)
        ;

        
    
    
            marker_a0b18219a46e347cccf911129796f3ed.bindTooltip(
                `<div>
                     大学城东路 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_a0b18219a46e347cccf911129796f3ed.setIcon(icon_9d8bb31633482add0702cb5472f73e00);
            
    
            var marker_39b41c83ffef5c949557b29b32455d11 = L.marker(
                [29.596149, 106.314804],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_f763a2ef931361bd522cc7b4ea669f69 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_129a467be37054076210a3e73b895406 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_604104d5bc6919e81a032cef28021595 = $(`<div id="html_604104d5bc6919e81a032cef28021595" style="width: 100.0%; height: 100.0%;">大学城东路3站<br>方向: 下行<br>序号: 7</div>`)[0];
                popup_129a467be37054076210a3e73b895406.setContent(html_604104d5bc6919e81a032cef28021595);
            
        

        marker_39b41c83ffef5c949557b29b32455d11.bindPopup(popup_129a467be37054076210a3e73b895406)
        ;

        
    
    
            marker_39b41c83ffef5c949557b29b32455d11.bindTooltip(
                `<div>
                     大学城东路3站 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_39b41c83ffef5c949557b29b32455d11.setIcon(icon_f763a2ef931361bd522cc7b4ea669f69);
            
    
            var marker_c3311ea0971b7394cb29d05372767745 = L.marker(
                [29.591357, 106.31451],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_299993e6e8f45354b04c8de66fd86f78 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_bd757d1b3ce262ca87eb70fdc7ce0921 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_91c9af41112983eb93e7110fe2d3741d = $(`<div id="html_91c9af41112983eb93e7110fe2d3741d" style="width: 100.0%; height: 100.0%;">重庆科技大学西门<br>方向: 下行<br>序号: 8</div>`)[0];
                popup_bd757d1b3ce262ca87eb70fdc7ce0921.setContent(html_91c9af41112983eb93e7110fe2d3741d);
            
        

        marker_c3311ea0971b7394cb29d05372767745.bindPopup(popup_bd757d1b3ce262ca87eb70fdc7ce0921)
        ;

        
    
    
            marker_c3311ea0971b7394cb29d05372767745.bindTooltip(
                `<div>
                     重庆科技大学西门 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_c3311ea0971b7394cb29d05372767745.setIcon(icon_299993e6e8f45354b04c8de66fd86f78);
            
    
            var marker_1c05a4e9b9b7bb0fbed45997d62f9b07 = L.marker(
                [29.588785, 106.314295],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_f6d291ffd0a853300de2f966a8b48fd6 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_37200d2f3b59b865af83cf44b89e921f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8be4800c788d556f11a09a969c092d24 = $(`<div id="html_8be4800c788d556f11a09a969c092d24" style="width: 100.0%; height: 100.0%;">大学城东路4站<br>方向: 下行<br>序号: 9</div>`)[0];
                popup_37200d2f3b59b865af83cf44b89e921f.setContent(html_8be4800c788d556f11a09a969c092d24);
            
        

        marker_1c05a4e9b9b7bb0fbed45997d62f9b07.bindPopup(popup_37200d2f3b59b865af83cf44b89e921f)
        ;

        
    
    
            marker_1c05a4e9b9b7bb0fbed45997d62f9b07.bindTooltip(
                `<div>
                     大学城东路4站 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_1c05a4e9b9b7bb0fbed45997d62f9b07.setIcon(icon_f6d291ffd0a853300de2f966a8b48fd6);
            
    
            var marker_2daf48e9d35bf1ed60e13b81f0b8f859 = L.marker(
                [29.58378, 106.313775],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_74bdc49732422ab6d1498049e80ec31a = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_5f6eedfe7887727cea6999281b94e4eb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0e62de2dadf4ddaec6c68746850fb965 = $(`<div id="html_0e62de2dadf4ddaec6c68746850fb965" style="width: 100.0%; height: 100.0%;">大学城东路南段<br>方向: 下行<br>序号: 10</div>`)[0];
                popup_5f6eedfe7887727cea6999281b94e4eb.setContent(html_0e62de2dadf4ddaec6c68746850fb965);
            
        

        marker_2daf48e9d35bf1ed60e13b81f0b8f859.bindPopup(popup_5f6eedfe7887727cea6999281b94e4eb)
        ;

        
    
    
            marker_2daf48e9d35bf1ed60e13b81f0b8f859.bindTooltip(
                `<div>
                     大学城东路南段 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_2daf48e9d35bf1ed60e13b81f0b8f859.setIcon(icon_74bdc49732422ab6d1498049e80ec31a);
            
    
            var marker_cc1513ce9c55ee7f29dba72a9d462a16 = L.marker(
                [29.580126, 106.312862],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_1607ebd8b94bc7283e48b7489719833a = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_48432e237c27a29329ed8fab5b086042 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_60ad4b20839b53ccf918166f33bd30b6 = $(`<div id="html_60ad4b20839b53ccf918166f33bd30b6" style="width: 100.0%; height: 100.0%;">康居西城<br>方向: 下行<br>序号: 11</div>`)[0];
                popup_48432e237c27a29329ed8fab5b086042.setContent(html_60ad4b20839b53ccf918166f33bd30b6);
            
        

        marker_cc1513ce9c55ee7f29dba72a9d462a16.bindPopup(popup_48432e237c27a29329ed8fab5b086042)
        ;

        
    
    
            marker_cc1513ce9c55ee7f29dba72a9d462a16.bindTooltip(
                `<div>
                     康居西城 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_cc1513ce9c55ee7f29dba72a9d462a16.setIcon(icon_1607ebd8b94bc7283e48b7489719833a);
            
    
            var marker_0976585673c6d8ee646538aed71719f1 = L.marker(
                [29.579152, 106.309097],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_ff8056c901238e9618973d3511c4da66 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_986ec06e616671d2a4a686e73a587459 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_15855f431981bba78007260ee5b69173 = $(`<div id="html_15855f431981bba78007260ee5b69173" style="width: 100.0%; height: 100.0%;">康居西城花市<br>方向: 下行<br>序号: 12</div>`)[0];
                popup_986ec06e616671d2a4a686e73a587459.setContent(html_15855f431981bba78007260ee5b69173);
            
        

        marker_0976585673c6d8ee646538aed71719f1.bindPopup(popup_986ec06e616671d2a4a686e73a587459)
        ;

        
    
    
            marker_0976585673c6d8ee646538aed71719f1.bindTooltip(
                `<div>
                     康居西城花市 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_0976585673c6d8ee646538aed71719f1.setIcon(icon_ff8056c901238e9618973d3511c4da66);
            
    
            var marker_18c5047ebe349880351d6b1e9c950e0a = L.marker(
                [29.575119, 106.306724],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_2e0b002ce1280a37c7dba6a9278e4299 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_43440d8888683a51ef9b6ebd690644c6 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b43e1529c511b61c05abc0081f1c9f55 = $(`<div id="html_b43e1529c511b61c05abc0081f1c9f55" style="width: 100.0%; height: 100.0%;">康城南路<br>方向: 下行<br>序号: 13</div>`)[0];
                popup_43440d8888683a51ef9b6ebd690644c6.setContent(html_b43e1529c511b61c05abc0081f1c9f55);
            
        

        marker_18c5047ebe349880351d6b1e9c950e0a.bindPopup(popup_43440d8888683a51ef9b6ebd690644c6)
        ;

        
    
    
            marker_18c5047ebe349880351d6b1e9c950e0a.bindTooltip(
                `<div>
                     康城南路 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_18c5047ebe349880351d6b1e9c950e0a.setIcon(icon_2e0b002ce1280a37c7dba6a9278e4299);
            
    
            var marker_d02f0801e64cd58c726cc29868089574 = L.marker(
                [29.565268, 106.303621],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_a77eb17753890082f8855ed5d7e2fe74 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_b6fa51dc8dfdd844a0ad4546c8ed7594 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9325c926c12598e56e3eebce8a4701f7 = $(`<div id="html_9325c926c12598e56e3eebce8a4701f7" style="width: 100.0%; height: 100.0%;">英业达<br>方向: 下行<br>序号: 14</div>`)[0];
                popup_b6fa51dc8dfdd844a0ad4546c8ed7594.setContent(html_9325c926c12598e56e3eebce8a4701f7);
            
        

        marker_d02f0801e64cd58c726cc29868089574.bindPopup(popup_b6fa51dc8dfdd844a0ad4546c8ed7594)
        ;

        
    
    
            marker_d02f0801e64cd58c726cc29868089574.bindTooltip(
                `<div>
                     英业达 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_d02f0801e64cd58c726cc29868089574.setIcon(icon_a77eb17753890082f8855ed5d7e2fe74);
            
    
            var marker_ed78ecd08e943c19bc766d35492e082d = L.marker(
                [29.560237, 106.299375],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_8feea5b2782ec167a8f2aa2908b67a06 = L.AwesomeMarkers.icon(
                {
  "markerColor": "blue",
  "iconColor": "white",
  "icon": "arrow-down",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_dbf125f6530e5004c969bafc95589f97 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5597263606d182d65729e62ff028cae3 = $(`<div id="html_5597263606d182d65729e62ff028cae3" style="width: 100.0%; height: 100.0%;">曾家转盘<br>方向: 下行<br>序号: 15</div>`)[0];
                popup_dbf125f6530e5004c969bafc95589f97.setContent(html_5597263606d182d65729e62ff028cae3);
            
        

        marker_ed78ecd08e943c19bc766d35492e082d.bindPopup(popup_dbf125f6530e5004c969bafc95589f97)
        ;

        
    
    
            marker_ed78ecd08e943c19bc766d35492e082d.bindTooltip(
                `<div>
                     曾家转盘 (下行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_ed78ecd08e943c19bc766d35492e082d.setIcon(icon_8feea5b2782ec167a8f2aa2908b67a06);
            
    
            var marker_0b4ae9a8d6311ed6975226acfaa74a2e = L.marker(
                [29.560237, 106.299375],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_d87fc3c10d3d99fd448731dad3b39539 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_a81a20d5a87b7d93a3d0ec8c6bb47445 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6033f1f8cacc1902bd9ba9e25741926d = $(`<div id="html_6033f1f8cacc1902bd9ba9e25741926d" style="width: 100.0%; height: 100.0%;">曾家转盘<br>方向: 上行<br>序号: 1</div>`)[0];
                popup_a81a20d5a87b7d93a3d0ec8c6bb47445.setContent(html_6033f1f8cacc1902bd9ba9e25741926d);
            
        

        marker_0b4ae9a8d6311ed6975226acfaa74a2e.bindPopup(popup_a81a20d5a87b7d93a3d0ec8c6bb47445)
        ;

        
    
    
            marker_0b4ae9a8d6311ed6975226acfaa74a2e.bindTooltip(
                `<div>
                     曾家转盘 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_0b4ae9a8d6311ed6975226acfaa74a2e.setIcon(icon_d87fc3c10d3d99fd448731dad3b39539);
            
    
            var marker_a081d880283585b5eb38c187b30cf70b = L.marker(
                [29.565841, 106.303813],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_2d753b2f570845f4a721ef012d388a9b = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_03ba92eb21f26d99d75b73af80b68255 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_69fc1976cf7dc79a88b3cf215f2b90ab = $(`<div id="html_69fc1976cf7dc79a88b3cf215f2b90ab" style="width: 100.0%; height: 100.0%;">英业达<br>方向: 上行<br>序号: 2</div>`)[0];
                popup_03ba92eb21f26d99d75b73af80b68255.setContent(html_69fc1976cf7dc79a88b3cf215f2b90ab);
            
        

        marker_a081d880283585b5eb38c187b30cf70b.bindPopup(popup_03ba92eb21f26d99d75b73af80b68255)
        ;

        
    
    
            marker_a081d880283585b5eb38c187b30cf70b.bindTooltip(
                `<div>
                     英业达 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_a081d880283585b5eb38c187b30cf70b.setIcon(icon_2d753b2f570845f4a721ef012d388a9b);
            
    
            var marker_84b03702c46146c8545f5a0e8e062562 = L.marker(
                [29.575142, 106.30689],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_db8546ccaf9759bfec1fb67ec73640bb = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_f91c703c975568bfb5a53f5f66e2fb10 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e2892d8941de556dfb89e0f1e03815bb = $(`<div id="html_e2892d8941de556dfb89e0f1e03815bb" style="width: 100.0%; height: 100.0%;">康城南路<br>方向: 上行<br>序号: 3</div>`)[0];
                popup_f91c703c975568bfb5a53f5f66e2fb10.setContent(html_e2892d8941de556dfb89e0f1e03815bb);
            
        

        marker_84b03702c46146c8545f5a0e8e062562.bindPopup(popup_f91c703c975568bfb5a53f5f66e2fb10)
        ;

        
    
    
            marker_84b03702c46146c8545f5a0e8e062562.bindTooltip(
                `<div>
                     康城南路 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_84b03702c46146c8545f5a0e8e062562.setIcon(icon_db8546ccaf9759bfec1fb67ec73640bb);
            
    
            var marker_762631300c36cfbd4dc5884d07092959 = L.marker(
                [29.579435, 106.309195],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_afe378008f90fb15b2fd7d9b62e9f077 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_630f362668dae7f915801f418eee201c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f7fb48f4f22afe3ee0b11310eef2d451 = $(`<div id="html_f7fb48f4f22afe3ee0b11310eef2d451" style="width: 100.0%; height: 100.0%;">康居西城花市<br>方向: 上行<br>序号: 4</div>`)[0];
                popup_630f362668dae7f915801f418eee201c.setContent(html_f7fb48f4f22afe3ee0b11310eef2d451);
            
        

        marker_762631300c36cfbd4dc5884d07092959.bindPopup(popup_630f362668dae7f915801f418eee201c)
        ;

        
    
    
            marker_762631300c36cfbd4dc5884d07092959.bindTooltip(
                `<div>
                     康居西城花市 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_762631300c36cfbd4dc5884d07092959.setIcon(icon_afe378008f90fb15b2fd7d9b62e9f077);
            
    
            var marker_7d407f2f9babc0a5c74a59e56ea7aa6a = L.marker(
                [29.580039, 106.312448],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_4cd6f0f9995c9ab4bf0ff8d6a9983f58 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_6aaea45022ed51851ebef954844982f2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b0520e9fa2536296184bf63f1c2bbe88 = $(`<div id="html_b0520e9fa2536296184bf63f1c2bbe88" style="width: 100.0%; height: 100.0%;">康居西城<br>方向: 上行<br>序号: 5</div>`)[0];
                popup_6aaea45022ed51851ebef954844982f2.setContent(html_b0520e9fa2536296184bf63f1c2bbe88);
            
        

        marker_7d407f2f9babc0a5c74a59e56ea7aa6a.bindPopup(popup_6aaea45022ed51851ebef954844982f2)
        ;

        
    
    
            marker_7d407f2f9babc0a5c74a59e56ea7aa6a.bindTooltip(
                `<div>
                     康居西城 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_7d407f2f9babc0a5c74a59e56ea7aa6a.setIcon(icon_4cd6f0f9995c9ab4bf0ff8d6a9983f58);
            
    
            var marker_4b6ee32adb9eb79986f0cd554fd1481f = L.marker(
                [29.584061, 106.314056],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_385b6b1cbe37bdea739db7dc9f2b6ee3 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_e5a8b09aaa96892a72bd9cbfd9afc573 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_91af639e4c4cc8aa85ac788b86530a65 = $(`<div id="html_91af639e4c4cc8aa85ac788b86530a65" style="width: 100.0%; height: 100.0%;">大学城东路南段<br>方向: 上行<br>序号: 6</div>`)[0];
                popup_e5a8b09aaa96892a72bd9cbfd9afc573.setContent(html_91af639e4c4cc8aa85ac788b86530a65);
            
        

        marker_4b6ee32adb9eb79986f0cd554fd1481f.bindPopup(popup_e5a8b09aaa96892a72bd9cbfd9afc573)
        ;

        
    
    
            marker_4b6ee32adb9eb79986f0cd554fd1481f.bindTooltip(
                `<div>
                     大学城东路南段 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_4b6ee32adb9eb79986f0cd554fd1481f.setIcon(icon_385b6b1cbe37bdea739db7dc9f2b6ee3);
            
    
            var marker_117ad031ad9271c3c506efb6ae55f788 = L.marker(
                [29.588717, 106.31437],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_1797658a1b00796701cdbbbd9f838f64 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_11c7a770cf039864acfc0cdf46314837 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_23f332e4919926837a49a4830f68a123 = $(`<div id="html_23f332e4919926837a49a4830f68a123" style="width: 100.0%; height: 100.0%;">大学城东路4站<br>方向: 上行<br>序号: 7</div>`)[0];
                popup_11c7a770cf039864acfc0cdf46314837.setContent(html_23f332e4919926837a49a4830f68a123);
            
        

        marker_117ad031ad9271c3c506efb6ae55f788.bindPopup(popup_11c7a770cf039864acfc0cdf46314837)
        ;

        
    
    
            marker_117ad031ad9271c3c506efb6ae55f788.bindTooltip(
                `<div>
                     大学城东路4站 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_117ad031ad9271c3c506efb6ae55f788.setIcon(icon_1797658a1b00796701cdbbbd9f838f64);
            
    
            var marker_fcf0d649aa511644de4c50788a82b4ce = L.marker(
                [29.591453, 106.314597],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_34edd516c894a1cb657b6158d6b88327 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_6bc7004f807da647dc89287272c4f596 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_082da90bbc95b3398b3acd647e98c7ad = $(`<div id="html_082da90bbc95b3398b3acd647e98c7ad" style="width: 100.0%; height: 100.0%;">重庆科技大学西门<br>方向: 上行<br>序号: 8</div>`)[0];
                popup_6bc7004f807da647dc89287272c4f596.setContent(html_082da90bbc95b3398b3acd647e98c7ad);
            
        

        marker_fcf0d649aa511644de4c50788a82b4ce.bindPopup(popup_6bc7004f807da647dc89287272c4f596)
        ;

        
    
    
            marker_fcf0d649aa511644de4c50788a82b4ce.bindTooltip(
                `<div>
                     重庆科技大学西门 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_fcf0d649aa511644de4c50788a82b4ce.setIcon(icon_34edd516c894a1cb657b6158d6b88327);
            
    
            var marker_657b8b86a9260efd7a6e1a0318b7d573 = L.marker(
                [29.596327, 106.314998],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_973320d151727601ea199a555d1bb5e0 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_f55c19b9c01c5061ed28cea736025144 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9566849a8d2471fcf8459269c2347e96 = $(`<div id="html_9566849a8d2471fcf8459269c2347e96" style="width: 100.0%; height: 100.0%;">大学城东路3站<br>方向: 上行<br>序号: 9</div>`)[0];
                popup_f55c19b9c01c5061ed28cea736025144.setContent(html_9566849a8d2471fcf8459269c2347e96);
            
        

        marker_657b8b86a9260efd7a6e1a0318b7d573.bindPopup(popup_f55c19b9c01c5061ed28cea736025144)
        ;

        
    
    
            marker_657b8b86a9260efd7a6e1a0318b7d573.bindTooltip(
                `<div>
                     大学城东路3站 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_657b8b86a9260efd7a6e1a0318b7d573.setIcon(icon_973320d151727601ea199a555d1bb5e0);
            
    
            var marker_cb353f6296a2042071296b4e6b2f9eeb = L.marker(
                [29.602871, 106.315634],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_9da1ca058facd5723c0b3f41a3b6a294 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_072b73ca4aed22acd3b2ebc9da744704 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fdb18ddea2893214afb84a606669cf67 = $(`<div id="html_fdb18ddea2893214afb84a606669cf67" style="width: 100.0%; height: 100.0%;">大学城东路<br>方向: 上行<br>序号: 10</div>`)[0];
                popup_072b73ca4aed22acd3b2ebc9da744704.setContent(html_fdb18ddea2893214afb84a606669cf67);
            
        

        marker_cb353f6296a2042071296b4e6b2f9eeb.bindPopup(popup_072b73ca4aed22acd3b2ebc9da744704)
        ;

        
    
    
            marker_cb353f6296a2042071296b4e6b2f9eeb.bindTooltip(
                `<div>
                     大学城东路 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_cb353f6296a2042071296b4e6b2f9eeb.setIcon(icon_9da1ca058facd5723c0b3f41a3b6a294);
            
    
            var marker_345305cd84aa3fcfafc8671b09b08fd1 = L.marker(
                [29.608858, 106.316412],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_1d928c4ba9c6fdb11b75a4d4f96514cd = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_3c8675da5bcb9190c1a7856bbb0fcbd1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_488ce7fe0dae14d8cb0e74cf961d65c8 = $(`<div id="html_488ce7fe0dae14d8cb0e74cf961d65c8" style="width: 100.0%; height: 100.0%;">大学城东路东<br>方向: 上行<br>序号: 11</div>`)[0];
                popup_3c8675da5bcb9190c1a7856bbb0fcbd1.setContent(html_488ce7fe0dae14d8cb0e74cf961d65c8);
            
        

        marker_345305cd84aa3fcfafc8671b09b08fd1.bindPopup(popup_3c8675da5bcb9190c1a7856bbb0fcbd1)
        ;

        
    
    
            marker_345305cd84aa3fcfafc8671b09b08fd1.bindTooltip(
                `<div>
                     大学城东路东 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_345305cd84aa3fcfafc8671b09b08fd1.setIcon(icon_1d928c4ba9c6fdb11b75a4d4f96514cd);
            
    
            var marker_d81ed075d9ba3b94937fd73da22de455 = L.marker(
                [29.609939, 106.325296],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_fdad151abc1c2835a77c1dc4846dc8f1 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_120c738bb5db96bebfef8437ce991a41 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fd7313d44216d3b45208ac54387e06f9 = $(`<div id="html_fd7313d44216d3b45208ac54387e06f9" style="width: 100.0%; height: 100.0%;">轨道陈家桥站<br>方向: 上行<br>序号: 12</div>`)[0];
                popup_120c738bb5db96bebfef8437ce991a41.setContent(html_fd7313d44216d3b45208ac54387e06f9);
            
        

        marker_d81ed075d9ba3b94937fd73da22de455.bindPopup(popup_120c738bb5db96bebfef8437ce991a41)
        ;

        
    
    
            marker_d81ed075d9ba3b94937fd73da22de455.bindTooltip(
                `<div>
                     轨道陈家桥站 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_d81ed075d9ba3b94937fd73da22de455.setIcon(icon_fdad151abc1c2835a77c1dc4846dc8f1);
            
    
            var marker_1aaa79597246b80868190776d0175630 = L.marker(
                [29.609783, 106.33666],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_e47c7e1c2da4af18eb9820dc0040923c = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_6f41c7234d8d09a6853d9d32ac8ff918 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_35f72c7aeb7cd6853825356373a97fab = $(`<div id="html_35f72c7aeb7cd6853825356373a97fab" style="width: 100.0%; height: 100.0%;">大学城北路东段路口<br>方向: 上行<br>序号: 13</div>`)[0];
                popup_6f41c7234d8d09a6853d9d32ac8ff918.setContent(html_35f72c7aeb7cd6853825356373a97fab);
            
        

        marker_1aaa79597246b80868190776d0175630.bindPopup(popup_6f41c7234d8d09a6853d9d32ac8ff918)
        ;

        
    
    
            marker_1aaa79597246b80868190776d0175630.bindTooltip(
                `<div>
                     大学城北路东段路口 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_1aaa79597246b80868190776d0175630.setIcon(icon_e47c7e1c2da4af18eb9820dc0040923c);
            
    
            var marker_2fd942b0be32f0bde546e2c03ae95aaf = L.marker(
                [29.612107, 106.34149],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_709ba686e3613defee18948b37febb7e = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_ad753eb1be52c7f8a5406c800913b228 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8182a61558df2a285cd6f3ae9e49278a = $(`<div id="html_8182a61558df2a285cd6f3ae9e49278a" style="width: 100.0%; height: 100.0%;">陈南路口<br>方向: 上行<br>序号: 14</div>`)[0];
                popup_ad753eb1be52c7f8a5406c800913b228.setContent(html_8182a61558df2a285cd6f3ae9e49278a);
            
        

        marker_2fd942b0be32f0bde546e2c03ae95aaf.bindPopup(popup_ad753eb1be52c7f8a5406c800913b228)
        ;

        
    
    
            marker_2fd942b0be32f0bde546e2c03ae95aaf.bindTooltip(
                `<div>
                     陈南路口 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_2fd942b0be32f0bde546e2c03ae95aaf.setIcon(icon_709ba686e3613defee18948b37febb7e);
            
    
            var marker_f2e431d1c99eb1ec929064a4c6d2a974 = L.marker(
                [29.616841, 106.339617],
                {
}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
            var icon_b1be754b0b1acee8073af1dde1221d76 = L.AwesomeMarkers.icon(
                {
  "markerColor": "red",
  "iconColor": "white",
  "icon": "arrow-up",
  "prefix": "fa",
  "extraClasses": "fa-rotate-0",
}
            );
        
    
        var popup_c07fe7b0b7f0be48af2e60f9663d60b7 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1097651267649c35dd1c2ca29d80c997 = $(`<div id="html_1097651267649c35dd1c2ca29d80c997" style="width: 100.0%; height: 100.0%;">陈家桥新医院<br>方向: 上行<br>序号: 15</div>`)[0];
                popup_c07fe7b0b7f0be48af2e60f9663d60b7.setContent(html_1097651267649c35dd1c2ca29d80c997);
            
        

        marker_f2e431d1c99eb1ec929064a4c6d2a974.bindPopup(popup_c07fe7b0b7f0be48af2e60f9663d60b7)
        ;

        
    
    
            marker_f2e431d1c99eb1ec929064a4c6d2a974.bindTooltip(
                `<div>
                     陈家桥新医院 (上行)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_f2e431d1c99eb1ec929064a4c6d2a974.setIcon(icon_b1be754b0b1acee8073af1dde1221d76);
            
    
            var poly_line_d76fb448136f4d2b9c6016dc089414be = L.polyline(
                [[29.617029, 106.339491], [29.611608, 106.341308], [29.610001, 106.33535], [29.610164, 106.324727], [29.608722, 106.316293], [29.602679, 106.315368], [29.596149, 106.314804], [29.591357, 106.31451], [29.588785, 106.314295], [29.58378, 106.313775], [29.580126, 106.312862], [29.579152, 106.309097], [29.575119, 106.306724], [29.565268, 106.303621], [29.560237, 106.299375]],
                {"bubblingMouseEvents": true, "color": "blue", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "blue", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.7, "smoothFactor": 1.0, "stroke": true, "weight": 3}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
        var popup_46a3cb67120261d0e1615aff17a252fa = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_71f94748e9167817e238636df9bf60db = $(`<div id="html_71f94748e9167817e238636df9bf60db" style="width: 100.0%; height: 100.0%;">下行路线</div>`)[0];
                popup_46a3cb67120261d0e1615aff17a252fa.setContent(html_71f94748e9167817e238636df9bf60db);
            
        

        poly_line_d76fb448136f4d2b9c6016dc089414be.bindPopup(popup_46a3cb67120261d0e1615aff17a252fa)
        ;

        
    
    
            var poly_line_259371f1992664f777476392e507bfef = L.polyline(
                [[29.560237, 106.299375], [29.565841, 106.303813], [29.575142, 106.30689], [29.579435, 106.309195], [29.580039, 106.312448], [29.584061, 106.314056], [29.588717, 106.31437], [29.591453, 106.314597], [29.596327, 106.314998], [29.602871, 106.315634], [29.608858, 106.316412], [29.609939, 106.325296], [29.609783, 106.33666], [29.612107, 106.34149], [29.616841, 106.339617]],
                {"bubblingMouseEvents": true, "color": "red", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "red", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.7, "smoothFactor": 1.0, "stroke": true, "weight": 3}
            ).addTo(map_7a2bae93d67e65a9df98689030436e48);
        
    
        var popup_09393a71b229d500a4d3731e5642b5ff = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_08d0ed10e3c131beec0744bb173e1b4b = $(`<div id="html_08d0ed10e3c131beec0744bb173e1b4b" style="width: 100.0%; height: 100.0%;">上行路线</div>`)[0];
                popup_09393a71b229d500a4d3731e5642b5ff.setContent(html_08d0ed10e3c131beec0744bb173e1b4b);
            
        

        poly_line_259371f1992664f777476392e507bfef.bindPopup(popup_09393a71b229d500a4d3731e5642b5ff)
        ;

        
    
</script>
</html>