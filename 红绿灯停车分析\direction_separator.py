#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公交车上行下行方向分离器
根据起点和终点坐标，分析车辆轨迹数据，区分上行和下行方向
"""

import pandas as pd
import os
import math

class DirectionSeparator:
    def __init__(self, start_point, end_point):
        """
        初始化方向分离器
        
        Args:
            start_point: 起点坐标 (longitude, latitude)
            end_point: 终点坐标 (longitude, latitude)
        """
        self.start_point = start_point  # (106.339491, 29.617029)
        self.end_point = end_point      # (106.299375, 29.560237)
        
        # 创建输出文件夹
        self.upward_dir = "上行数据"
        self.downward_dir = "下行数据"
        os.makedirs(self.upward_dir, exist_ok=True)
        os.makedirs(self.downward_dir, exist_ok=True)
    
    def calculate_distance(self, point1, point2):
        """
        计算两点间的距离（米）
        使用Haversine公式
        """
        lat1, lon1 = math.radians(point1[1]), math.radians(point1[0])
        lat2, lon2 = math.radians(point2[1]), math.radians(point2[0])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371000  # 地球半径（米）
        
        return c * r
    

    
    def passes_through_point(self, trip_data, target_point, threshold=30):
        """
        判断行程是否经过指定点

        Args:
            trip_data: 单次行程的GPS数据
            target_point: 目标点坐标 (longitude, latitude)
            threshold: 距离阈值（米）

        Returns:
            bool: 是否经过该点
        """
        for _, row in trip_data.iterrows():
            point = (row['Longitude'], row['Latitude'])
            distance = self.calculate_distance(point, target_point)
            if distance <= threshold:
                return True
        return False

    def analyze_trip_direction(self, trip_data):
        """
        分析单次行程的方向
        严格要求：
        1. 必须经过对应的必经点（±40米）
        2. 起点和终点必须在设定范围内

        Args:
            trip_data: 单次行程的GPS数据

        Returns:
            'upward', 'downward', 或 'exclude'（不符合条件的行程）
        """
        if len(trip_data) < 5:  # 至少需要5个数据点
            return 'exclude'

        # 获取行程的起点和终点
        trip_start = (trip_data.iloc[0]['Longitude'], trip_data.iloc[0]['Latitude'])
        trip_end = (trip_data.iloc[-1]['Longitude'], trip_data.iloc[-1]['Latitude'])

        # 检查起点和终点是否在设定范围内
        start_near_start_point = self.calculate_distance(trip_start, self.start_point) <= 60
        start_near_end_point = self.calculate_distance(trip_start, self.end_point) <= 60
        end_near_start_point = self.calculate_distance(trip_end, self.start_point) <= 60
        end_near_end_point = self.calculate_distance(trip_end, self.end_point) <= 60

        # 定义特定路径点（调整到±40米）
        downward_point = (106.299398, 29.563687)  # 下行必经点
        upward_point = (106.303990, 29.561718)    # 上行必经点

        # 检查是否经过特定路径点
        passes_downward_point = self.passes_through_point(trip_data, downward_point, 40)
        passes_upward_point = self.passes_through_point(trip_data, upward_point, 40)

        # 判断下行：从起点到终点，必须经过下行必经点
        if (start_near_start_point and end_near_end_point and
            passes_downward_point and not passes_upward_point):
            return 'downward'

        # 判断上行：从终点到起点，必须经过上行必经点
        elif (start_near_end_point and end_near_start_point and
              passes_upward_point and not passes_downward_point):
            return 'upward'

        else:
            # 不符合严格条件，排除
            return 'exclude'
    

    
    def is_near_terminal_point(self, point, threshold=60):
        """
        判断点是否接近起点或终点
        """
        dist_to_start = self.calculate_distance(point, self.start_point)
        dist_to_end = self.calculate_distance(point, self.end_point)
        return dist_to_start <= threshold or dist_to_end <= threshold

    def validate_trip_continuity(self, trip_data):
        """
        验证行程的连续性，相邻两点距离不能超过500米
        """
        for i in range(1, len(trip_data)):
            prev_point = (trip_data.iloc[i-1]['Longitude'], trip_data.iloc[i-1]['Latitude'])
            curr_point = (trip_data.iloc[i]['Longitude'], trip_data.iloc[i]['Latitude'])
            distance = self.calculate_distance(prev_point, curr_point)
            if distance > 500:
                return False
        return True

    def split_into_strict_trips(self, df):
        """
        严格按照起点终点分割行程
        每个行程必须：
        1. 从起点或终点60米范围内开始
        2. 在终点或起点60米范围内结束
        3. 连续2个数据点进入起点/终点范围时开始记录
        4. 连续2个数据点进入终点/起点范围时结束记录
        """
        trips = []

        # 过滤掉无效坐标
        df = df[(df['Longitude'] != 0.0) & (df['Latitude'] != 0.0)].copy()
        df['GpsTime'] = pd.to_datetime(df['GpsTime'])
        df = df.sort_values('GpsTime').reset_index(drop=True)

        if len(df) == 0:
            return trips

        current_trip = []
        start_area_count = 0
        end_area_count = 0
        recording = False

        for i in range(len(df)):
            point = (df.iloc[i]['Longitude'], df.iloc[i]['Latitude'])

            # 检查是否在起点或终点区域
            near_start = self.calculate_distance(point, self.start_point) <= 60
            near_end = self.calculate_distance(point, self.end_point) <= 60

            if near_start or near_end:
                if not recording:
                    # 可能开始新行程
                    if near_start:
                        start_area_count += 1
                        end_area_count = 0
                    else:
                        end_area_count += 1
                        start_area_count = 0

                    # 连续2个点在起点或终点区域，开始记录
                    if start_area_count >= 2 or end_area_count >= 2:
                        recording = True
                        # 往前数2条数据作为行程开始
                        start_index = max(0, i - 1)
                        current_trip = list(range(start_index, i + 1))
                        start_area_count = 0
                        end_area_count = 0
                else:
                    # 正在记录中，检查是否到达另一个终点
                    current_trip.append(i)

                    # 检查是否到达终点（与起点不同的点）
                    if len(current_trip) > 10:  # 确保有足够的行程长度
                        trip_start_point = (df.iloc[current_trip[0]]['Longitude'], df.iloc[current_trip[0]]['Latitude'])
                        start_near_start = self.calculate_distance(trip_start_point, self.start_point) <= 60

                        if start_near_start and near_end:
                            # 从起点开始，到达终点
                            end_area_count += 1
                            if end_area_count >= 2:
                                # 完成一个行程
                                if len(current_trip) > 5:
                                    trip_data = df.iloc[current_trip].copy()
                                    if self.validate_trip_continuity(trip_data):
                                        trips.append(trip_data)
                                    else:
                                        print(f"  警告：行程存在超过500米的跳跃，已排除")

                                # 重置状态
                                recording = False
                                current_trip = []
                                start_area_count = 0
                                end_area_count = 0

                        elif not start_near_start and near_start:
                            # 从终点开始，到达起点
                            start_area_count += 1
                            if start_area_count >= 2:
                                # 完成一个行程
                                if len(current_trip) > 5:
                                    trip_data = df.iloc[current_trip].copy()
                                    if self.validate_trip_continuity(trip_data):
                                        trips.append(trip_data)
                                    else:
                                        print(f"  警告：行程存在超过500米的跳跃，已排除")

                                # 重置状态
                                recording = False
                                current_trip = []
                                start_area_count = 0
                                end_area_count = 0
                        else:
                            start_area_count = 0
                            end_area_count = 0
            else:
                # 不在起点或终点区域
                start_area_count = 0
                end_area_count = 0

                if recording:
                    current_trip.append(i)

        return trips

    def split_into_trips(self, df):
        """
        将GPS数据分割成严格的起点终点行程
        """
        # 使用严格的分割逻辑
        all_trips = self.split_into_strict_trips(df)

        # 按行程开始时间排序
        if all_trips:
            all_trips.sort(key=lambda x: x.iloc[0]['GpsTime'])

        return all_trips
    
    def process_vehicle_file(self, file_path):
        """
        处理单个车辆文件
        """
        print(f"正在处理文件: {file_path}")
        
        try:
            # 读取数据
            df = pd.read_csv(file_path)
            
            # 分割成行程
            trips = self.split_into_trips(df)
            print(f"  发现 {len(trips)} 个行程")
            
            upward_trips = []
            downward_trips = []
            excluded_trips = 0

            # 分析每个行程的方向
            for i, trip in enumerate(trips):
                # 直接分析行程方向
                direction = self.analyze_trip_direction(trip)

                print(f"  行程 {i+1}: {direction}")

                if direction == 'upward':
                    upward_trips.append(trip)
                elif direction == 'downward':
                    downward_trips.append(trip)
                elif direction == 'exclude':
                    excluded_trips += 1
            
            # 保存分离后的数据
            filename = os.path.basename(file_path)
            
            if upward_trips:
                upward_data = pd.concat(upward_trips, ignore_index=True)
                upward_file = os.path.join(self.upward_dir, filename)
                upward_data.to_csv(upward_file, index=False)
                print(f"  上行数据保存到: {upward_file} ({len(upward_data)} 条记录)")
            
            if downward_trips:
                downward_data = pd.concat(downward_trips, ignore_index=True)
                downward_file = os.path.join(self.downward_dir, filename)
                downward_data.to_csv(downward_file, index=False)
                print(f"  下行数据保存到: {downward_file} ({len(downward_data)} 条记录)")

            print(f"  排除的行程数: {excluded_trips}")

            # 检查上下行比例
            if len(upward_trips) > 0 and len(downward_trips) > 0:
                ratio = max(len(upward_trips), len(downward_trips)) / min(len(upward_trips), len(downward_trips))
                if ratio > 3:
                    print(f"  警告：上下行比例失衡 (上行:{len(upward_trips)}, 下行:{len(downward_trips)}, 比例:{ratio:.1f}:1)")
            elif len(upward_trips) == 0:
                print(f"  警告：没有识别到上行行程")
            elif len(downward_trips) == 0:
                print(f"  警告：没有识别到下行行程")

            return len(upward_trips), len(downward_trips), excluded_trips
            
        except Exception as e:
            print(f"  处理文件时出错: {e}")
            return 0, 0, 0
    
    def process_all_files(self, data_dir):
        """
        处理指定目录下的所有车辆数据文件
        """
        print(f"开始处理目录: {data_dir}")
        print(f"起点坐标: {self.start_point}")
        print(f"终点坐标: {self.end_point}")
        print("-" * 50)
        
        total_upward = 0
        total_downward = 0
        total_excluded = 0
        processed_files = 0

        # 遍历所有txt文件
        for filename in os.listdir(data_dir):
            if filename.endswith('.txt') and filename.startswith('CarNum_'):
                file_path = os.path.join(data_dir, filename)
                upward_count, downward_count, excluded_count = self.process_vehicle_file(file_path)
                total_upward += upward_count
                total_downward += downward_count
                total_excluded += excluded_count
                processed_files += 1
                print()

        print("=" * 50)
        print(f"处理完成！")
        print(f"处理文件数: {processed_files}")
        print(f"总上行行程数: {total_upward}")
        print(f"总下行行程数: {total_downward}")
        print(f"总排除行程数: {total_excluded}")

        # 检查总体上下行比例
        if total_upward > 0 and total_downward > 0:
            total_ratio = max(total_upward, total_downward) / min(total_upward, total_downward)
            print(f"总体上下行比例: {total_ratio:.1f}:1")
            if total_ratio > 3:
                print(f"警告：总体上下行比例失衡，可能需要调整判断条件")

        print(f"上行数据保存在: {self.upward_dir}")
        print(f"下行数据保存在: {self.downward_dir}")
        print(f"\n严格判断条件:")
        print(f"1. 下行: 从起点(106.339491, 29.617029)到终点(106.299375, 29.560237)")
        print(f"   - 必经点: (106.299398, 29.563687) ±40米")
        print(f"   - 行程起点必须在起点60米范围内")
        print(f"   - 行程终点必须在终点60米范围内")
        print(f"2. 上行: 从终点(106.299375, 29.560237)到起点(106.339491, 29.617029)")
        print(f"   - 必经点: (106.303990, 29.561718) ±40米")
        print(f"   - 行程起点必须在终点60米范围内")
        print(f"   - 行程终点必须在起点60米范围内")
        print(f"3. 相邻数据点距离不超过500米")
        print(f"4. 必须经过对应必经点，不能同时经过两个必经点")
        print(f"5. 连续2个数据点进入起点/终点范围时开始/结束记录")

def main():
    # 设置起点和终点坐标
    start_point = (106.339491, 29.617029)  # 起点
    end_point = (106.299375, 29.560237)    # 终点
    
    # 创建方向分离器
    separator = DirectionSeparator(start_point, end_point)
    
    # 处理车辆数据
    data_directory = "红绿灯停车分析/车辆数据"
    separator.process_all_files(data_directory)

if __name__ == "__main__":
    main()
