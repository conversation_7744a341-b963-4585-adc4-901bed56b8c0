import pandas as pd
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_intersection_data(upward_folder, downward_folder):
    """加载上行和下行路口数据"""
    all_data = []
    excluded_car = ''  # 排除的车辆编号23486

    # 处理上行数据
    if os.path.exists(upward_folder):
        for file in os.listdir(upward_folder):
            if file.endswith('.txt'):
                car_num = file.replace('CarNum_', '').replace('.txt', '')
                if car_num == excluded_car:
                    print(f"跳过车辆 {car_num} 的数据")
                    continue
                file_path = os.path.join(upward_folder, file)
                try:
                    df = pd.read_csv(file_path)
                    df['Direction'] = '上行'
                    df['CarNum'] = car_num
                    all_data.append(df)
                except Exception as e:
                    print(f"读取文件 {file} 时出错: {e}")

    # 处理下行数据
    if os.path.exists(downward_folder):
        for file in os.listdir(downward_folder):
            if file.endswith('.txt'):
                car_num = file.replace('CarNum_', '').replace('.txt', '')
                if car_num == excluded_car:
                    print(f"跳过车辆 {car_num} 的数据")
                    continue
                file_path = os.path.join(downward_folder, file)
                try:
                    df = pd.read_csv(file_path)
                    df['Direction'] = '下行'
                    df['CarNum'] = car_num
                    all_data.append(df)
                except Exception as e:
                    print(f"读取文件 {file} 时出错: {e}")

    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df['GpsTime'] = pd.to_datetime(combined_df['GpsTime'])
        return combined_df
    else:
        return pd.DataFrame()

def remove_outliers_iqr(data, column):
    """使用IQR方法移除异常值"""
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR

    # 记录异常值信息
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    if len(outliers) > 0:
        print(f"检测到 {len(outliers)} 个异常值，范围: {outliers[column].min():.1f} - {outliers[column].max():.1f} 秒")
        print(f"正常值范围: {lower_bound:.1f} - {upper_bound:.1f} 秒")

    # 返回去除异常值后的数据
    return data[(data[column] >= lower_bound) & (data[column] <= upper_bound)]

def calculate_transit_times(df):
    """计算每辆车每次通过路口的时间"""
    transit_data = []

    for car_num in df['CarNum'].unique():
        for direction in df['Direction'].unique():
            car_data = df[(df['CarNum'] == car_num) & (df['Direction'] == direction)].copy()
            car_data = car_data.sort_values('GpsTime')

            if len(car_data) < 2:
                continue

            # 识别每次通过路口的时间段（基于时间间隔）
            time_diffs = car_data['GpsTime'].diff().dt.total_seconds()
            # 如果时间间隔超过30分钟，认为是新的通过事件
            break_points = time_diffs > 1800  # 30分钟

            trip_groups = break_points.cumsum()

            for trip_id in trip_groups.unique():
                trip_data = car_data[trip_groups == trip_id]
                if len(trip_data) >= 2:
                    start_time = trip_data['GpsTime'].min()
                    end_time = trip_data['GpsTime'].max()
                    transit_time = (end_time - start_time).total_seconds()

                    # 只考虑合理的通行时间（10秒到20分钟）
                    if 10 <= transit_time <= 1200:
                        transit_data.append({
                            'CarNum': car_num,
                            'Direction': direction,
                            'TransitTime': transit_time,
                            'StartTime': start_time,
                            'EndTime': end_time
                        })

    transit_df = pd.DataFrame(transit_data)

    # 移除异常值
    if not transit_df.empty:
        print(f"\n异常值检测前: {len(transit_df)} 条通行记录")
        transit_df_clean = remove_outliers_iqr(transit_df, 'TransitTime')
        print(f"异常值检测后: {len(transit_df_clean)} 条通行记录")
        return transit_df_clean

    return transit_df

def analyze_bus_performance(transit_df):
    """分析公交车通行性能"""
    performance_stats = []

    print("\n各车辆异常值检测详情:")
    for car_num in transit_df['CarNum'].unique():
        car_data = transit_df[transit_df['CarNum'] == car_num].copy()

        if len(car_data) > 5:  # 至少需要5个数据点才进行异常值检测
            print(f"\n车辆 {car_num}:")
            original_count = len(car_data)
            car_data_clean = remove_outliers_iqr(car_data, 'TransitTime')

            if len(car_data_clean) > 0:
                avg_time = car_data_clean['TransitTime'].mean()
                std_time = car_data_clean['TransitTime'].std()
                min_time = car_data_clean['TransitTime'].min()
                max_time = car_data_clean['TransitTime'].max()
                count = len(car_data_clean)

                performance_stats.append({
                    'CarNum': car_num,
                    'AvgTransitTime': avg_time,
                    'StdTransitTime': std_time if not pd.isna(std_time) else 0,
                    'MinTransitTime': min_time,
                    'MaxTransitTime': max_time,
                    'TransitCount': count,
                    'OriginalCount': original_count,
                    'RemovedOutliers': original_count - count
                })
        elif len(car_data) > 0:
            # 数据点太少，不进行异常值检测
            print(f"\n车辆 {car_num}: 数据点太少({len(car_data)}个)，跳过异常值检测")
            avg_time = car_data['TransitTime'].mean()
            std_time = car_data['TransitTime'].std()
            min_time = car_data['TransitTime'].min()
            max_time = car_data['TransitTime'].max()
            count = len(car_data)

            performance_stats.append({
                'CarNum': car_num,
                'AvgTransitTime': avg_time,
                'StdTransitTime': std_time if not pd.isna(std_time) else 0,
                'MinTransitTime': min_time,
                'MaxTransitTime': max_time,
                'TransitCount': count,
                'OriginalCount': count,
                'RemovedOutliers': 0
            })

    return pd.DataFrame(performance_stats)

def create_bar_chart(performance_df):
    """创建平均通行时间条形图"""
    plt.figure(figsize=(12, 8))
    performance_sorted = performance_df.sort_values('AvgTransitTime')
    bars = plt.bar(range(len(performance_sorted)), performance_sorted['AvgTransitTime'],
                   color='skyblue', alpha=0.8, edgecolor='navy', linewidth=1)

    plt.xlabel('公交车编号', fontsize=14, fontweight='bold')
    plt.ylabel('平均通行时间 (秒)', fontsize=14, fontweight='bold')
    plt.title('各公交车平均通行时间对比', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(range(len(performance_sorted)), performance_sorted['CarNum'], fontsize=12)
    plt.yticks(fontsize=12)

    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('1_平均通行时间条形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_box_plot(performance_df, transit_df):
    """创建通行时间分布箱形图（使用清理后的数据）"""
    plt.figure(figsize=(14, 8))
    box_data = []
    box_labels = []

    # 按平均通行时间排序
    performance_sorted = performance_df.sort_values('AvgTransitTime')
    for car_num in performance_sorted['CarNum']:
        # 对每个车辆的数据单独进行异常值清理
        car_data = transit_df[transit_df['CarNum'] == car_num].copy()
        if len(car_data) > 5:
            car_data_clean = remove_outliers_iqr(car_data, 'TransitTime')
            if len(car_data_clean) > 0:
                box_data.append(car_data_clean['TransitTime'])
                box_labels.append(car_num)
        elif len(car_data) > 0:
            # 数据点太少，不进行异常值检测
            box_data.append(car_data['TransitTime'])
            box_labels.append(car_num)

    if box_data:
        bp = plt.boxplot(box_data, patch_artist=True)
        colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink',
                 'lightgray', 'lightcyan', 'wheat', 'lavender', 'peachpuff', 'thistle', 'honeydew']
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.8)

        # 设置x轴标签
        plt.xticks(range(1, len(box_labels) + 1), box_labels, fontsize=12, rotation=45)

    plt.xlabel('公交车编号', fontsize=14, fontweight='bold')
    plt.ylabel('通行时间 (秒)', fontsize=14, fontweight='bold')
    plt.title('各公交车通行时间分布箱形图（已剔除异常值）', fontsize=16, fontweight='bold', pad=20)
    plt.yticks(fontsize=12)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('2_通行时间分布箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_scatter_plot(performance_df):
    """创建稳定性散点图"""
    plt.figure(figsize=(12, 8))
    plt.scatter(performance_df['AvgTransitTime'], performance_df['StdTransitTime'],
               c='red', alpha=0.7, s=120, edgecolors='darkred', linewidth=2)

    plt.xlabel('平均通行时间 (秒)', fontsize=14, fontweight='bold')
    plt.ylabel('通行时间标准差 (秒)', fontsize=14, fontweight='bold')
    plt.title('通行效率 vs 稳定性散点图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)

    # 添加车辆编号标签
    for i, row in performance_df.iterrows():
        plt.annotate(f'车辆{row["CarNum"]}',
                    (row['AvgTransitTime'], row['StdTransitTime']),
                    xytext=(8, 8), textcoords='offset points',
                    fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('3_稳定性散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_visualizations(performance_df, transit_df):
    """创建所有可视化图表"""
    print("正在生成平均通行时间条形图...")
    create_bar_chart(performance_df)

    print("正在生成通行时间分布箱形图...")
    create_box_plot(performance_df, transit_df)

    print("正在生成稳定性散点图...")
    create_scatter_plot(performance_df)

def main():
    """主函数"""
    # 设置文件夹路径
    upward_folder = "按交叉口分组的数据_上行/5-大学城北路-大学城东一路"
    downward_folder = "按交叉口分组的数据_下行/3-大学城北路大学城东一路"
    
    print("正在加载数据...")
    # 加载数据
    df = load_intersection_data(upward_folder, downward_folder)
    
    if df.empty:
        print("未找到数据文件！")
        return
    
    print(f"成功加载数据，共 {len(df)} 条记录")
    print(f"涉及公交车数量: {df['CarNum'].nunique()}")
    print(f"数据时间范围: {df['GpsTime'].min()} 到 {df['GpsTime'].max()}")
    
    # 计算通行时间
    print("\n正在计算通行时间...")
    transit_df = calculate_transit_times(df)
    
    if transit_df.empty:
        print("未能计算出有效的通行时间数据！")
        return
    
    print(f"计算出 {len(transit_df)} 次有效通行记录")
    
    # 分析性能
    print("\n正在分析公交车性能...")
    performance_df = analyze_bus_performance(transit_df)
    
    print("\n=== 公交车通行效率分析结果（已剔除异常值）===")
    # 显示详细的统计信息
    display_df = performance_df[['CarNum', 'AvgTransitTime', 'StdTransitTime', 'MinTransitTime',
                                'MaxTransitTime', 'TransitCount', 'RemovedOutliers']].round(2)
    print(display_df)

    # 显示异常值处理汇总
    total_original = performance_df['OriginalCount'].sum()
    total_removed = performance_df['RemovedOutliers'].sum()
    total_final = performance_df['TransitCount'].sum()
    print(f"\n=== 异常值处理汇总 ===")
    print(f"原始数据总数: {total_original}")
    print(f"移除异常值数量: {total_removed}")
    print(f"最终分析数据: {total_final}")
    print(f"异常值比例: {(total_removed/total_original)*100:.1f}%")
    
    # 创建可视化
    print("\n正在生成可视化图表...")
    create_visualizations(performance_df, transit_df)

    print("\n分析完成！已生成以下图表文件：")
    print("1. 1_平均通行时间条形图.png")
    print("2. 2_通行时间分布箱形图.png")
    print("3. 3_稳定性散点图.png")

if __name__ == "__main__":
    main()
