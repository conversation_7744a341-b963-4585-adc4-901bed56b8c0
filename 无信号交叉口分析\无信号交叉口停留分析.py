import os
import csv
import math
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, time
from collections import defaultdict
from scipy.interpolate import interp1d

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 地球半径（米）
EARTH_RADIUS = 6371000

# 无信号交叉口坐标和名称
INTERSECTIONS = {
    "康城南路-康城南路": (29.575424, 106.307123),
    "康城南路-康城北路": (29.577980,106.309754),
    "康城路-大学城东路": (29.580101,106.313592),
    "大学城东路春华秋实": (29.588250,106.314287)
}

# 交叉口范围半径（米）
INTERSECTION_RADIUS = 30


def haversine_distance(lat1, lon1, lat2, lon2):
    """计算两点间的距离（米）"""
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    return EARTH_RADIUS * c


def get_time_period(gps_time):
    """根据时间判断时段：早高峰、平峰、晚高峰"""
    t = gps_time.time()
    
    # 早高峰 6:30-9:00
    if time(6, 30) <= t < time(9, 0):
        return "早高峰(6:30-9:00)"
    
    # 平峰 10:00-15:00
    elif time(10, 0) <= t < time(15, 0):
        return "平峰(10:00-15:00)"
    
    # 晚高峰 16:30-19:00
    elif time(16, 30) <= t < time(19, 0):
        return "晚高峰(16:30-19:00)"
    
    # 其他时段忽略
    return None


def interpolate_data(data_points):
    """对数据进行插值，将10秒间隔变为5秒间隔"""
    if len(data_points) < 2:
        return data_points
    
    interpolated_data = []
    
    for i in range(len(data_points) - 1):
        current_point = data_points[i]
        next_point = data_points[i + 1]
        
        # 添加当前点
        interpolated_data.append(current_point)
        
        # 计算时间差
        time_diff = (next_point[0] - current_point[0]).total_seconds()
        
        # 如果时间差大于5秒且小于等于15秒，进行插值
        if 5 < time_diff <= 15:
            # 计算中间时间点
            mid_time = current_point[0] + (next_point[0] - current_point[0]) / 2
            
            # 插值经纬度（线性插值）
            mid_lon = (current_point[1] + next_point[1]) / 2
            mid_lat = (current_point[2] + next_point[2]) / 2
            
            # 插值速度（考虑驾驶习惯，使用平滑过渡）
            # 如果两点速度都为0，中间点也为0
            if current_point[3] == 0 and next_point[3] == 0:
                mid_speed = 0
            # 如果一个为0一个不为0，使用较小值的一半
            elif current_point[3] == 0 or next_point[3] == 0:
                mid_speed = max(current_point[3], next_point[3]) / 2
            # 否则使用平均值
            else:
                mid_speed = (current_point[3] + next_point[3]) / 2
            
            # 添加插值点
            interpolated_data.append((mid_time, mid_lon, mid_lat, mid_speed))
    
    # 添加最后一个点
    interpolated_data.append(data_points[-1])
    
    return interpolated_data


def filter_intersection_data(vehicle_file, intersection_name, intersection_coord):
    """筛选在交叉口范围内的数据"""
    data = []
    
    with open(vehicle_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过标题行
        
        for row in reader:
            if len(row) < 5:
                continue
            try:
                gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                longitude = float(row[2])
                latitude = float(row[3])
                speed = float(row[4])
                
                # 计算到交叉口的距离
                distance = haversine_distance(latitude, longitude, 
                                            intersection_coord[0], intersection_coord[1])
                
                # 如果在范围内，添加到数据中
                if distance <= INTERSECTION_RADIUS:
                    data.append((gps_time, longitude, latitude, speed))
                    
            except ValueError:
                continue
    
    if not data:
        return []
    
    # 按时间排序
    data.sort(key=lambda x: x[0])
    
    # 进行插值
    interpolated_data = interpolate_data(data)
    
    return interpolated_data


def analyze_intersection_passages(intersection_data):
    """分析交叉口通过情况"""
    if not intersection_data:
        return []

    # 按日期分组
    daily_data = defaultdict(list)
    for gps_time, lon, lat, speed in intersection_data:
        date_key = gps_time.date()
        daily_data[date_key].append((gps_time, lon, lat, speed))

    passages = []

    # 定义连续通过的时间间隔阈值（秒）
    # 如果两个数据点之间的时间间隔超过这个阈值，则认为是两次不同的通过
    TIME_GAP_THRESHOLD = 300  # 5分钟

    for date_key, day_data in daily_data.items():
        # 按时段分组
        time_period_data = defaultdict(list)
        for gps_time, lon, lat, speed in day_data:
            time_period = get_time_period(gps_time)
            if time_period:
                time_period_data[time_period].append((gps_time, lon, lat, speed))

        # 分析每个时段的通过情况
        for time_period, period_data in time_period_data.items():
            if len(period_data) < 2:  # 至少需要2个点才能算通过
                continue

            # 将数据按连续通过分组
            passage_groups = []
            current_group = [period_data[0]]

            for i in range(1, len(period_data)):
                current_point = period_data[i]
                last_point = current_group[-1]

                # 计算时间间隔
                time_gap = (current_point[0] - last_point[0]).total_seconds()

                # 如果时间间隔小于阈值，添加到当前组
                if time_gap <= TIME_GAP_THRESHOLD:
                    current_group.append(current_point)
                # 否则，结束当前组并开始新组
                else:
                    if len(current_group) >= 2:  # 至少需要2个点才能算一次通过
                        passage_groups.append(current_group)
                    current_group = [current_point]

            # 添加最后一组
            if len(current_group) >= 2:
                passage_groups.append(current_group)

            # 分析每组通过情况
            for group in passage_groups:
                # 计算通过时间
                start_time = group[0][0]
                end_time = group[-1][0]
                transit_time = (end_time - start_time).total_seconds()

                # 检查是否有停留（速度为0）
                has_stop = any(speed == 0 for _, _, _, speed in group)

                # 只记录合理的通过时间（小于30分钟）
                if transit_time <= 1800:  # 30分钟
                    passages.append({
                        'date': date_key,
                        'time_period': time_period,
                        'start_time': start_time,
                        'end_time': end_time,
                        'transit_time': transit_time,
                        'has_stop': has_stop,
                        'data_points': len(group)
                    })
    
    return passages


def process_all_intersections():
    """处理所有交叉口的数据"""
    input_dir = '下行数据'
    output_dir = '无信号交叉口插值数据_下行'
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取所有车辆文件
    vehicle_files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
    print(f"找到 {len(vehicle_files)} 个车辆文件")
    
    # 存储所有分析结果
    all_results = {}
    summary_data = []
    
    for intersection_name, intersection_coord in INTERSECTIONS.items():
        print(f"\n正在处理交叉口: {intersection_name}")
        
        # 为每个交叉口创建子目录
        intersection_dir = os.path.join(output_dir, intersection_name)
        if not os.path.exists(intersection_dir):
            os.makedirs(intersection_dir)
        
        intersection_passages = []
        
        for vehicle_file in vehicle_files:
            vehicle_path = os.path.join(input_dir, vehicle_file)
            car_num = vehicle_file.replace('CarNum_', '').replace('.txt', '')
            
            # 筛选和插值数据
            intersection_data = filter_intersection_data(vehicle_path, intersection_name, intersection_coord)
            
            if intersection_data:
                # 保存插值后的数据
                output_file = os.path.join(intersection_dir, f'CarNum_{car_num}_interpolated.txt')
                with open(output_file, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['CarNum', 'GpsTime', 'Longitude', 'Latitude', 'GpsSpeed'])
                    for gps_time, lon, lat, speed in intersection_data:
                        writer.writerow([car_num, gps_time.strftime('%Y-%m-%d %H:%M:%S'), 
                                       lon, lat, speed])
                
                # 分析通过情况
                passages = analyze_intersection_passages(intersection_data)
                intersection_passages.extend(passages)
        
        # 统计该交叉口的结果
        if intersection_passages:
            stats = analyze_intersection_stats(intersection_passages)
            all_results[intersection_name] = stats
            
            # 添加到汇总数据
            for time_period, period_stats in stats.items():
                summary_data.append({
                    '交叉口': intersection_name,
                    '时段': time_period,
                    '总通过次数': period_stats['total_passages'],
                    '停留次数': period_stats['stop_count'],
                    '停留概率(%)': round(period_stats['stop_probability'], 2),
                    '平均通过时间(秒)': round(period_stats['avg_transit_time'], 2),
                    '平均通过时间(分钟)': round(period_stats['avg_transit_time'] / 60, 2)
                })
    
    return all_results, summary_data


def analyze_intersection_stats(passages):
    """分析交叉口统计数据"""
    stats = defaultdict(lambda: {
        'total_passages': 0,
        'stop_count': 0,
        'total_transit_time': 0.0,
        'transit_times': []
    })
    
    for passage in passages:
        time_period = passage['time_period']
        stats[time_period]['total_passages'] += 1
        stats[time_period]['total_transit_time'] += passage['transit_time']
        stats[time_period]['transit_times'].append(passage['transit_time'])
        
        if passage['has_stop']:
            stats[time_period]['stop_count'] += 1
    
    # 计算统计指标
    for time_period in stats:
        period_stats = stats[time_period]
        total_passages = period_stats['total_passages']
        stop_count = period_stats['stop_count']
        total_transit_time = period_stats['total_transit_time']
        
        period_stats['stop_probability'] = (stop_count / total_passages * 100) if total_passages > 0 else 0
        period_stats['avg_transit_time'] = total_transit_time / total_passages if total_passages > 0 else 0
    
    return dict(stats)


def create_visualizations(df_summary):
    """创建可视化图表"""
    print("\n正在生成可视化图表...")
    
    # 1. 停留概率热力图
    plt.figure(figsize=(12, 8))
    pivot_stop_prob = df_summary.pivot(index='交叉口', columns='时段', values='停留概率(%)')
    
    # 确保时段顺序
    time_periods_order = ["早高峰(6:30-9:00)", "平峰(10:00-15:00)", "晚高峰(16:30-19:00)"]
    pivot_stop_prob = pivot_stop_prob.reindex(columns=time_periods_order)
    
    sns.heatmap(pivot_stop_prob, annot=True, fmt='.1f', cmap='YlOrRd',
                cbar_kws={'label': '停留概率 (%)'})
    plt.title('无信号交叉口停留概率热力图', fontsize=16, fontweight='bold')
    plt.xlabel('时段', fontsize=12)
    plt.ylabel('交叉口', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('无信号交叉口停留概率热力图_下行.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 平均通过时间热力图
    plt.figure(figsize=(12, 8))
    pivot_transit_time = df_summary.pivot(index='交叉口', columns='时段', values='平均通过时间(分钟)')
    pivot_transit_time = pivot_transit_time.reindex(columns=time_periods_order)
    
    sns.heatmap(pivot_transit_time, annot=True, fmt='.2f', cmap='Blues',
                cbar_kws={'label': '平均通过时间 (分钟)'})
    plt.title('无信号交叉口平均通过时间热力图', fontsize=16, fontweight='bold')
    plt.xlabel('时段', fontsize=12)
    plt.ylabel('交叉口', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('无信号交叉口平均通过时间热力图_下行.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("可视化图表已生成并保存！")


def main():
    """主处理流程"""
    print("开始处理无信号交叉口停留分析...")
    print(f"分析的交叉口: {list(INTERSECTIONS.keys())}")
    print(f"交叉口范围半径: {INTERSECTION_RADIUS}米")
    
    # 处理所有交叉口
    all_results, summary_data = process_all_intersections()
    
    # 保存结果
    df_summary = pd.DataFrame(summary_data)
    df_summary = df_summary.sort_values(['交叉口', '时段'])
    df_summary.to_csv('无信号交叉口停留分析结果_下行.csv', index=False, encoding='utf_8_sig')
    print(f"\n分析结果已保存到: 无信号交叉口停留分析结果_下行.csv")
    
    # 打印结果
    print("\n=== 无信号交叉口停留分析结果 ===")
    for intersection_name in INTERSECTIONS.keys():
        if intersection_name in all_results:
            print(f"\n【{intersection_name}】")
            stats = all_results[intersection_name]
            for time_period in ["早高峰(6:30-9:00)", "平峰(10:00-15:00)", "晚高峰(16:30-19:00)"]:
                if time_period in stats:
                    period_stats = stats[time_period]
                    print(f"  {time_period}:")
                    print(f"    总通过次数: {period_stats['total_passages']}")
                    print(f"    停留次数: {period_stats['stop_count']}")
                    print(f"    停留概率: {period_stats['stop_probability']:.1f}%")
                    print(f"    平均通过时间: {period_stats['avg_transit_time']:.1f}秒 ({period_stats['avg_transit_time']/60:.2f}分钟)")
    
    # 生成可视化
    if not df_summary.empty:
        create_visualizations(df_summary)
    
    print("\n分析完成！")


if __name__ == '__main__':
    main()
