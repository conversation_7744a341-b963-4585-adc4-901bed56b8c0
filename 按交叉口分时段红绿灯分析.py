import os
import csv
import math
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, time
from collections import defaultdict

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 地球半径（米）
EARTH_RADIUS = 6371000


def get_time_period(start_time):
    """根据起始时间判断时段 - 从6:00开始每隔1.5小时划分一个时段到21:00"""
    t = start_time.time()

    # 6:00-7:30
    if time(6, 0) <= t < time(7, 30):
        return "06:00-07:30"
    
    # 7:30-9:00
    elif time(7, 30) <= t < time(9, 0):
        return "07:30-09:00"
    
    # 9:00-10:30
    elif time(9, 0) <= t < time(10, 30):
        return "09:00-10:30"
    
    # 10:30-12:00
    elif time(10, 30) <= t < time(12, 0):
        return "10:30-12:00"
    
    # 12:00-13:30
    elif time(12, 0) <= t < time(13, 30):
        return "12:00-13:30"
    
    # 13:30-15:00
    elif time(13, 30) <= t < time(15, 0):
        return "13:30-15:00"
    
    # 15:00-16:30
    elif time(15, 0) <= t < time(16, 30):
        return "15:00-16:30"
    
    # 16:30-18:00
    elif time(16, 30) <= t < time(18, 0):
        return "16:30-18:00"
    
    # 18:00-19:30
    elif time(18, 0) <= t < time(19, 30):
        return "18:00-19:30"
    
    # 19:30-21:00
    elif time(19, 30) <= t < time(21, 0):
        return "19:30-21:00"

    # 其他时段忽略不计
    return None


def analyze_intersection_data(intersection_folder):
    """分析单个交叉口的数据"""
    intersection_name = os.path.basename(intersection_folder)
    print(f"正在分析交叉口: {intersection_name}")

    # 存储该交叉口的统计数据
    time_period_stats = defaultdict(lambda: {
        'stop_events': 0,
        'total_stop_time': 0.0,
        'total_passages': 0,  # 总通过次数（每辆车每天每个时段的通过次数）
        'vehicles_with_stops': 0,  # 有停车的通过次数
        'max_stop_time': 0.0,  # 最长停留时间
    })

    # 获取该交叉口下的所有车辆文件
    vehicle_files = [f for f in os.listdir(intersection_folder) if f.endswith('.txt')]

    for vehicle_file in vehicle_files:
        vehicle_path = os.path.join(intersection_folder, vehicle_file)
        car_num = vehicle_file.replace('CarNum_', '').replace('.txt', '')

        # 读取车辆数据
        data = []
        with open(vehicle_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过标题行
            for row in reader:
                if len(row) < 5:
                    continue
                try:
                    gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                    speed = float(row[4])
                    data.append((gps_time, speed))
                except ValueError:
                    continue

        if not data:
            continue

        # 按时间排序
        data.sort(key=lambda x: x[0])

        # 按日期分组分析（每天单独分析）
        daily_data = defaultdict(list)
        for gps_time, speed in data:
            date_key = gps_time.date()
            daily_data[date_key].append((gps_time, speed))

        # 分析每天的数据
        for date_key, day_data in daily_data.items():
            # 按时段分析该车该天的通过情况
            time_period_passages = defaultdict(list)

            for gps_time, speed in day_data:
                time_period = get_time_period(gps_time)
                if time_period:
                    time_period_passages[time_period].append((gps_time, speed))

            # 分析每个时段的停车情况（每辆车每天每个时段只算一次通过）
            for time_period, passage_data in time_period_passages.items():
                if not passage_data:
                    continue

                # 记录该车辆在该时段有一次通过
                time_period_stats[time_period]['total_passages'] += 1

                # 分析这次通过的停车情况
                has_stop_event = False
                total_stop_time_this_passage = 0.0
                max_single_stop_time = 0.0

                i = 0
                while i < len(passage_data):
                    gps_time, speed = passage_data[i]

                    # 检查是否停车（速度<5km/h）
                    if speed <= 5.0:
                        # 找到停车事件的开始和结束
                        stop_start_time = gps_time
                        stop_end_time = gps_time
                        j = i

                        # 向后查找停车结束时间
                        while j < len(passage_data) and passage_data[j][1] <= 5.0:
                            stop_end_time = passage_data[j][0]
                            j += 1

                        # 计算停车时间
                        stop_duration = (stop_end_time - stop_start_time).total_seconds()

                        # 如果停车时间超过1秒，记录为停车
                        if stop_duration >= 1:
                            has_stop_event = True
                            total_stop_time_this_passage += stop_duration
                            # 更新单次最长停车时间
                            max_single_stop_time = max(max_single_stop_time, stop_duration)

                        i = j  # 跳过已处理的停车时间段
                    else:
                        i += 1

                # 如果这次通过有停车，记录停车事件和停车时间
                if has_stop_event:
                    time_period_stats[time_period]['stop_events'] += 1
                    time_period_stats[time_period]['total_stop_time'] += total_stop_time_this_passage
                    time_period_stats[time_period]['vehicles_with_stops'] += 1
                    # 更新该时段的最长停车时间
                    time_period_stats[time_period]['max_stop_time'] = max(
                        time_period_stats[time_period]['max_stop_time'],
                        max_single_stop_time
                    )

    return intersection_name, dict(time_period_stats)


def main():
    """主处理流程"""
    # 按交叉口分组的数据目录
    intersections_dir = '按交叉口分组的数据_下行'
    
    if not os.path.exists(intersections_dir):
        print(f"错误：找不到目录 {intersections_dir}")
        return
    
    # 获取所有交叉口文件夹
    intersection_folders = [f for f in os.listdir(intersections_dir) 
                           if os.path.isdir(os.path.join(intersections_dir, f))]
    
    print(f"找到 {len(intersection_folders)} 个交叉口")
    
    # 存储所有分析结果
    all_results = {}
    summary_data = []
    
    # 分析每个交叉口
    for intersection_folder in intersection_folders:
        intersection_path = os.path.join(intersections_dir, intersection_folder)
        intersection_name, stats = analyze_intersection_data(intersection_path)
        all_results[intersection_name] = stats
        
        # 生成汇总数据
        for time_period, period_stats in stats.items():
            total_passages = period_stats['total_passages']
            stop_events = period_stats['stop_events']
            total_stop_time = period_stats['total_stop_time']
            max_stop_time = period_stats['max_stop_time']

            # 计算平均停留时间（总停留时间除以有停留的次数）
            avg_stop_time_per_stop_event = (total_stop_time / stop_events) if stop_events > 0 else 0

            # 计算停车概率
            stop_probability = (stop_events / total_passages * 100) if total_passages > 0 else 0

            summary_data.append({
                '交叉口': intersection_name,
                '时段': time_period,
                '总通过次数': total_passages,
                '停车次数': stop_events,
                '停车概率(%)': round(stop_probability, 2),
                '总停留时间(秒)': round(total_stop_time, 2),
                '平均停留时间(秒)': round(avg_stop_time_per_stop_event, 2),
                '平均停留时间(分钟)': round(avg_stop_time_per_stop_event / 60, 2),
                '最长停留时间(秒)': round(max_stop_time, 2),
                '最长停留时间(分钟)': round(max_stop_time / 60, 2)
            })
    
    # 保存汇总结果到CSV
    df_summary = pd.DataFrame(summary_data)
    df_summary = df_summary.sort_values(['交叉口', '时段'])
    df_summary.to_csv('按交叉口分时段红绿灯分析结果.csv', index=False, encoding='utf_8_sig')
    print(f"\n分析结果已保存到: 按交叉口分时段红绿灯分析结果.csv")
    
    # 打印汇总统计
    print("\n=== 按交叉口分时段红绿灯分析结果 ===")
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00", 
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00", 
                         "18:00-19:30", "19:30-21:00"]
    
    for intersection_name in sorted(all_results.keys()):
        print(f"\n【{intersection_name}】")
        stats = all_results[intersection_name]
        for time_period in time_periods_order:
            if time_period in stats:
                period_stats = stats[time_period]
                total_passages = period_stats['total_passages']
                stop_events = period_stats['stop_events']
                total_stop_time = period_stats['total_stop_time']
                
                # 计算平均停留时间（总停留时间除以有停留的次数）
                avg_stop_time = (total_stop_time / stop_events) if stop_events > 0 else 0
                stop_probability = (stop_events / total_passages * 100) if total_passages > 0 else 0
                
                print(f"  {time_period}:")
                print(f"    总通过次数: {total_passages}")
                print(f"    停车次数: {stop_events}")
                print(f"    停车概率: {stop_probability:.1f}%")
                print(f"    平均停留时间: {avg_stop_time:.1f}秒 ({avg_stop_time/60:.2f}分钟)")
    
    # 生成可视化
    create_visualizations(df_summary)


def extract_intersection_number(intersection_name):
    """从交叉口名称中提取前面的数字编号"""
    import re
    # 使用正则表达式提取开头的数字
    match = re.match(r'^(\d+)', intersection_name)
    if match:
        return int(match.group(1))
    else:
        # 如果没有找到数字，返回一个很大的数，让它排在最后
        return 999


def create_visualizations(df_summary):
    """创建可视化图表"""
    print("\n正在生成可视化图表...")

    # 设置图表样式和中文字体
    plt.style.use('default')
    sns.set_palette("husl")

    # 尝试设置中文字体
    try:
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    except:
        print("警告：无法设置中文字体，可能影响中文显示")

    # 1. 停车概率热力图
    plt.figure(figsize=(15, 10))
    pivot_stop_prob = df_summary.pivot(index='交叉口', columns='时段', values='停车概率(%)')

    # 确保时段顺序正确
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00",
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00",
                         "18:00-19:30", "19:30-21:00"]
    pivot_stop_prob = pivot_stop_prob.reindex(columns=time_periods_order)

    # 按交叉口名称前的数字编号排序
    intersection_names = list(pivot_stop_prob.index)
    intersection_names_sorted = sorted(intersection_names, key=extract_intersection_number)
    print(f"交叉口排序前: {intersection_names}")
    print(f"交叉口排序后: {intersection_names_sorted}")
    pivot_stop_prob = pivot_stop_prob.reindex(index=intersection_names_sorted)

    sns.heatmap(pivot_stop_prob, annot=True, fmt='.1f', cmap='YlOrRd',
                cbar_kws={'label': 'Stop Probability (%)'})
    plt.title('Intersection Stop Probability Heatmap by Time Period', fontsize=16, fontweight='bold')
    plt.xlabel('Time Period', fontsize=12)
    plt.ylabel('Intersection', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('交叉口停车概率热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 平均停留时间热力图
    plt.figure(figsize=(15, 10))
    pivot_stop_time = df_summary.pivot(index='交叉口', columns='时段', values='平均停留时间(分钟)')
    pivot_stop_time = pivot_stop_time.reindex(columns=time_periods_order)

    # 按交叉口名称前的数字编号排序
    pivot_stop_time = pivot_stop_time.reindex(index=intersection_names_sorted)

    sns.heatmap(pivot_stop_time, annot=True, fmt='.2f', cmap='Blues',
                cbar_kws={'label': 'Average Stop Time (minutes)'})
    plt.title('Intersection Average Stop Time Heatmap by Time Period', fontsize=16, fontweight='bold')
    plt.xlabel('Time Period', fontsize=12)
    plt.ylabel('Intersection', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('交叉口平均停留时间热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("可视化图表已生成并保存！")


if __name__ == '__main__':
    main()
