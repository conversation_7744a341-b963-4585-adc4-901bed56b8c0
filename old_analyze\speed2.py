import pandas as pd
import folium
import branca.colormap as cm
import numpy as np

# 读取数据
df = pd.read_csv('car_23178.csv')

# 数据清洗：过滤无效坐标（经纬度为0）
df = df[(df['Longitude'] != 0) & (df['Latitude'] != 0)]

# 分箱处理
# 特殊处理速度为0的情况
max_speed = df['GpsSpeed'].max()
bins = [0] + list(range(5, int(np.ceil(max_speed/5))*5 + 1, 5))  # 创建分箱边界
labels = [f'{bins[i]}-{bins[i+1]}' for i in range(len(bins)-1)]  # 创建标签

# 使用pd.cut进行分箱，包含right参数处理边界
df['speed_bin'] = pd.cut(df['GpsSpeed'], bins=bins, include_lowest=True, right=False)

# 创建颜色映射
# 采用蓝绿色系，确保颜色数量足够
base_colors = ['#08306b', '#2171b5', '#4292c6', '#6baed6', '#9ecae1', '#c6dbef']
# 根据分箱数量扩展颜色列表（循环复用）
colors = (base_colors * (len(bins) // len(base_colors) + 1))[:len(bins)]

# 创建颜色映射字典
# 使用区间左端点作为键（转换为字符串），避免直接访问.cat属性
speed_colors = {str(interval.left): colors[i] for i, interval in enumerate(df['speed_bin'].cat.categories)}

# 创建颜色条
colormap = cm.StepColormap(
    colors=colors,
    index=bins,
    vmin=0,
    vmax=max_speed,
    caption='Speed (m/s)'
)

# 初始化地图视图（使用数据平均经纬度）
m = folium.Map(location=[df['Latitude'].mean(), df['Longitude'].mean()], zoom_start=13)

# 添加标记
for _, row in df.iterrows():
    # 处理速度为0的特殊情况
    if row['GpsSpeed'] == 0:
        color = '#d94701'  # 使用橙色表示速度为0
    else:
        # 使用区间左端点获取颜色
        color = speed_colors.get(str(row['speed_bin'].left), '#gray')  # 默认灰色作为后备颜色
    
    folium.CircleMarker(
        location=[row['Latitude'], row['Longitude']],
        radius=3,
        color=color,
        fill=True,
        fill_color=color,
        fill_opacity=0.7,
        opacity=0.7,
        popup=f"Speed: {row['GpsSpeed']} m/s"
    ).add_to(m)

# 添加颜色条
m.add_child(colormap)

# 保存为html文件
m.save('speed_map.html')