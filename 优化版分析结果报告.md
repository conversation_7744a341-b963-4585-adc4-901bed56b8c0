# 公交车速度分布分析报告（优化版）

## 分析概述

本次分析针对指定路段（起点：29.585958, 106.314104；终点：29.590679, 106.314488）的公交车速度分布进行了详细研究，采用了优化的可视化方案和更精细的分段策略。

## 优化改进

### 1. 颜色方案优化
- **新方案**: 每5km/h使用一个颜色，共10个颜色等级
- **颜色映射**: 从深红色(0-5km/h)到极深绿色(45+km/h)的渐变
- **优势**: 更直观地反映速度差异，便于识别不同速度区间

### 2. 分段精度提升
- **原方案**: 50米分段，共11段
- **新方案**: 30米分段，共18段
- **优势**: 更精细的空间分辨率，能捕捉更细微的速度变化

### 3. 零速度点可视化
- **新增功能**: 在地图上用70%透明度的红色小点标出速度为0的位置
- **意义**: 直观显示停车或拥堵位置

### 4. 图例信息增强
- **新增内容**: 在图例中显示每段的数据点数量和零速度点数量
- **优势**: 提供更全面的数据密度信息

### 5. 速度区间分布图
- **新增图表**: 横坐标为速度区间，纵坐标为数据点数量的柱状图
- **用途**: 分析整体速度分布特征

## 分析参数

- **起点坐标**: (29.585958, 106.314104)
- **终点坐标**: (29.590679, 106.314488)
- **路段总长度**: 524.6 米
- **识别半径**: 20米（用于筛选经过起终点的公交车）
- **走廊宽度**: 30米（左右各15米）
- **分段长度**: 30米（优化后）
- **总段数**: 18段（优化后）

## 数据筛选结果

从上行数据文件夹中成功识别出 **13辆公交车** 经过指定路段：
- CarNum_23178, CarNum_23484, CarNum_23485, CarNum_23486, CarNum_23487
- CarNum_23488, CarNum_23490, CarNum_23491, CarNum_23492, CarNum_23493
- CarNum_23494, CarNum_23497, CarNum_23498

所有筛选出的数据已保存到 `filtered_route_data/` 文件夹中。

## 速度分析结果

### 整体统计
- **有效分析段数**: 18段（全部段都有数据）
- **平均速度范围**: 2.30 - 30.54 km/h
- **整体平均速度**: 15.75 km/h
- **总数据点**: 21,211个

### 速度区间分布统计（每5km/h一个区间）
| 速度区间 | 数据点数量 | 占比 | 颜色编码 |
|----------|------------|------|----------|
| 0-5 km/h | 10,955 | 51.6% | 深红色 |
| 5-10 km/h | 990 | 4.7% | 红色 |
| 10-15 km/h | 1,705 | 8.0% | 橙红色 |
| 15-20 km/h | 2,522 | 11.9% | 橙色 |
| 20-25 km/h | 1,980 | 9.3% | 金色 |
| 25-30 km/h | 1,568 | 7.4% | 绿黄色 |
| 30-35 km/h | 976 | 4.6% | 酸橙绿 |
| 35-40 km/h | 421 | 2.0% | 森林绿 |
| 40-45 km/h | 90 | 0.4% | 深绿色 |
| 45+ km/h | 4 | 0.0% | 极深绿色 |

### 关键发现

1. **低速占主导**: 超过一半的数据点(51.6%)速度在0-5km/h区间，表明该路段存在显著的拥堵或停车现象
2. **速度分布不均**: 15-20km/h区间占比最高(11.9%)，其次是20-25km/h(9.3%)和10-15km/h(8.0%)
3. **高速点稀少**: 40km/h以上的高速点仅占0.4%，说明该路段整体车速较低
4. **空间变化明显**: 不同路段间速度差异显著，最快段比最慢段快13倍

### 各段详细分析（30米分段）

**高速段（>25km/h）**:
- 段5-6: 速度最高区域(30.31-30.54 km/h)，可能是道路条件最好的路段
- 段7: 27.73 km/h，仍属高速区域

**中速段（10-25km/h）**:
- 段1, 3, 4, 8, 9, 10, 12, 13, 16, 17, 18: 分布较为分散

**低速段（<10km/h）**:
- 段2: 4.46 km/h，起始区域低速
- 段11: 2.30 km/h，最慢段，数据点最多(4,355个)，可能是主要拥堵点
- 段14-15: 8.64-3.23 km/h，接近终点的低速区域

## 交通特征分析

1. **拥堵热点**: 段11和段15是主要的低速区域，数据点密集，可能存在交通信号灯或瓶颈
2. **流畅路段**: 段5-7为高速流畅区域，道路条件较好
3. **速度梯度**: 从起点到终点呈现复杂的速度变化模式，不是简单的加速或减速过程

## 输出文件

1. **交互式地图**: `speed_analysis_results/speed_distribution_map.html`
   - 每5km/h颜色编码的路段显示
   - 70%透明度的零速度点标记
   - 详细的图例，包含各段数据点数量

2. **速度分布图表**: `speed_analysis_results/speed_distribution_chart.png`
   - 各段平均速度条形图
   - 与地图相同的颜色编码

3. **速度区间分布图**: `speed_analysis_results/speed_interval_distribution.png`
   - 每5km/h区间的数据点数量统计
   - 直观显示整体速度分布特征

4. **详细数据**: `speed_analysis_results/speed_distribution_data.csv`
   - 18段的详细分析数据
   - 包含坐标、速度、数据点数量等信息

5. **筛选数据**: `filtered_route_data/`
   - 13辆公交车的原始轨迹数据

## 建议

1. **重点关注低速段**: 对段11和段15进行实地调研，确认拥堵原因
2. **学习高速段经验**: 分析段5-7的道路特征，为改善其他路段提供参考
3. **时间维度分析**: 建议进一步分析不同时段的速度变化模式
4. **零速度点分析**: 深入研究零速度点的分布规律，识别具体的停车原因
