import pandas as pd
import numpy as np

# 读取分析结果
df = pd.read_csv('车辆驾驶风格分析结果.csv', encoding='utf-8')

print('=== 14辆公交车驾驶风格聚类分析报告 ===')
print()

print('📊 分析概述:')
print('   - 分析了14辆公交车在上行和下行路线的驾驶表现')
print('   - 基于多维度指标进行K-means聚类，成功分为三种驾驶类型')
print('   - 每辆车平均有34趟行程数据（上行17趟，下行17趟）')
print()

# 按驾驶风格分组统计
style_summary = df.groupby('driving_style').agg({
    'car_num': 'count',
    'avg_speed_mean': ['mean', 'std'],
    'max_speed_mean': ['mean', 'std'],
    'driving_efficiency_mean': ['mean', 'std'],
    'stop_count_mean': ['mean', 'std'],
    'aggressiveness_score_mean': ['mean', 'std'],
    'conservativeness_score_mean': ['mean', 'std'],
    'speed_volatility_mean': ['mean', 'std'],
    'rapid_acceleration_count_mean': ['mean', 'std'],
    'rapid_deceleration_count_mean': ['mean', 'std']
}).round(3)

print('1. 🚗 三种驾驶类型总体特征对比:')
print()

for style in ['激进型', '正常型', '保守型']:
    if style in df['driving_style'].values:
        style_data = df[df['driving_style'] == style]
        vehicle_count = len(style_data)
        vehicles = ', '.join(style_data['car_num'].astype(str))
        
        print(f'【{style}】({vehicle_count}辆车)')
        print(f'  车辆编号: {vehicles}')
        print(f'  平均速度: {style_data["avg_speed_mean"].mean():.1f} ± {style_data["avg_speed_mean"].std():.1f} km/h')
        print(f'  最高速度: {style_data["max_speed_mean"].mean():.1f} ± {style_data["max_speed_mean"].std():.1f} km/h')
        print(f'  驾驶效率: {style_data["driving_efficiency_mean"].mean():.3f} ± {style_data["driving_efficiency_mean"].std():.3f}')
        print(f'  停车频率: {style_data["stop_count_mean"].mean():.1f} ± {style_data["stop_count_mean"].std():.1f} 次/趟')
        print(f'  速度波动: {style_data["speed_volatility_mean"].mean():.1f} ± {style_data["speed_volatility_mean"].std():.1f}')
        print(f'  急加速次数: {style_data["rapid_acceleration_count_mean"].mean():.1f} ± {style_data["rapid_acceleration_count_mean"].std():.1f} 次/趟')
        print(f'  急减速次数: {style_data["rapid_deceleration_count_mean"].mean():.1f} ± {style_data["rapid_deceleration_count_mean"].std():.1f} 次/趟')
        print()

print('2. 📈 关键指标详细分析:')
print()

print('🏃 平均速度排名:')
speed_ranking = df.sort_values('avg_speed_mean', ascending=False)[['car_num', 'driving_style', 'avg_speed_mean']]
for i, (_, row) in enumerate(speed_ranking.iterrows(), 1):
    print(f'  {i:2d}. 车辆{row["car_num"]} ({row["driving_style"]}): {row["avg_speed_mean"]:.2f} km/h')

print()
print('⚡ 驾驶效率排名:')
efficiency_ranking = df.sort_values('driving_efficiency_mean', ascending=False)[['car_num', 'driving_style', 'driving_efficiency_mean']]
for i, (_, row) in enumerate(efficiency_ranking.iterrows(), 1):
    print(f'  {i:2d}. 车辆{row["car_num"]} ({row["driving_style"]}): {row["driving_efficiency_mean"]:.3f}')

print()
print('🛑 停车频率排名（从少到多）:')
stop_ranking = df.sort_values('stop_count_mean', ascending=True)[['car_num', 'driving_style', 'stop_count_mean']]
for i, (_, row) in enumerate(stop_ranking.iterrows(), 1):
    print(f'  {i:2d}. 车辆{row["car_num"]} ({row["driving_style"]}): {row["stop_count_mean"]:.1f} 次/趟')

print()
print('3. 🔄 上行vs下行表现对比:')
print()

for style in ['激进型', '正常型', '保守型']:
    if style in df['driving_style'].values:
        style_data = df[df['driving_style'] == style]
        
        print(f'【{style}】上行vs下行对比:')
        
        # 计算上行下行的平均值
        upward_speed = style_data['avg_speed_upward'].mean()
        downward_speed = style_data['avg_speed_downward'].mean()
        upward_stops = style_data['stop_count_upward'].mean()
        downward_stops = style_data['stop_count_downward'].mean()
        upward_efficiency = style_data['driving_efficiency_upward'].mean()
        downward_efficiency = style_data['driving_efficiency_downward'].mean()
        
        print(f'  平均速度: 上行 {upward_speed:.1f} km/h vs 下行 {downward_speed:.1f} km/h')
        print(f'  停车次数: 上行 {upward_stops:.1f} 次 vs 下行 {downward_stops:.1f} 次')
        print(f'  驾驶效率: 上行 {upward_efficiency:.3f} vs 下行 {downward_efficiency:.3f}')
        print()

print('4. 🎯 驾驶行为特征分析:')
print()

print('【激进型驾驶特征】')
if '激进型' in df['driving_style'].values:
    aggressive_data = df[df['driving_style'] == '激进型']
    print('  ✅ 优势特征:')
    print(f'    - 最高的平均速度: {aggressive_data["avg_speed_mean"].mean():.1f} km/h')
    print(f'    - 最高的驾驶效率: {aggressive_data["driving_efficiency_mean"].mean():.3f}')
    print(f'    - 最少的停车次数: {aggressive_data["stop_count_mean"].mean():.1f} 次/趟')
    print('  ⚠️ 需要注意:')
    print(f'    - 速度波动较大: {aggressive_data["speed_volatility_mean"].mean():.1f}')
    print(f'    - 急加速次数: {aggressive_data["rapid_acceleration_count_mean"].mean():.1f} 次/趟')

print()
print('【正常型驾驶特征】')
if '正常型' in df['driving_style'].values:
    normal_data = df[df['driving_style'] == '正常型']
    print('  ✅ 平衡特征:')
    print(f'    - 适中的平均速度: {normal_data["avg_speed_mean"].mean():.1f} km/h')
    print(f'    - 适中的驾驶效率: {normal_data["driving_efficiency_mean"].mean():.3f}')
    print(f'    - 适中的停车频率: {normal_data["stop_count_mean"].mean():.1f} 次/趟')
    print('  📊 稳定性:')
    print(f'    - 速度波动: {normal_data["speed_volatility_mean"].mean():.1f}')
    print(f'    - 急加速次数: {normal_data["rapid_acceleration_count_mean"].mean():.1f} 次/趟')

print()
print('【保守型驾驶特征】')
if '保守型' in df['driving_style'].values:
    conservative_data = df[df['driving_style'] == '保守型']
    print('  ✅ 安全特征:')
    print(f'    - 相对较低的平均速度: {conservative_data["avg_speed_mean"].mean():.1f} km/h')
    print(f'    - 较低的速度波动: {conservative_data["speed_volatility_mean"].mean():.1f}')
    print('  ⚠️ 效率考虑:')
    print(f'    - 较低的驾驶效率: {conservative_data["driving_efficiency_mean"].mean():.3f}')
    print(f'    - 较多的停车次数: {conservative_data["stop_count_mean"].mean():.1f} 次/趟')

print()
print('5. 💡 管理建议:')
print()
print('【针对激进型驾驶员】')
print('  • 保持高效率的同时，注意控制速度波动')
print('  • 减少急加速急减速，提高乘客舒适度')
print('  • 可作为效率标杆，分享经验给其他驾驶员')

print()
print('【针对正常型驾驶员】')
print('  • 表现均衡，可作为培训的标准参考')
print('  • 适当学习激进型的高效率技巧')
print('  • 保持当前的稳定驾驶风格')

print()
print('【针对保守型驾驶员】')
print('  • 在保证安全的前提下，适当提高行驶效率')
print('  • 学习减少不必要的停车，提高流畅性')
print('  • 可通过培训提升驾驶技能')

print()
print('【整体优化建议】')
print('  • 建立驾驶员分类管理制度')
print('  • 定期进行驾驶行为分析和反馈')
print('  • 组织不同类型驾驶员之间的经验交流')
print('  • 根据路线特点匹配合适的驾驶员类型')

print()
print('=== 分析完成 ===')
print('详细数据请查看: 车辆驾驶风格分析结果.csv')
print('可视化图表: 驾驶风格综合分析.png, 车辆表现热力图.png')
