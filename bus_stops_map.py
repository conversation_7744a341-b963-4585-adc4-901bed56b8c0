import folium
import webbrowser
import os

# 下行数据
downstream_data = [
    ["陈家桥新医院", 106.339491, 29.617029, "下行"],
    ["陈南路口", 106.341308, 29.611608, "下行"],
    ["大学城北路东段路口", 106.335350, 29.610001, "下行"],
    ["轨道陈家桥站", 106.324727, 29.610164, "下行"],
    ["大学城东路东", 106.316293, 29.608722, "下行"],
    ["大学城东路", 106.315368, 29.602679, "下行"],
    ["大学城东路3站", 106.314804, 29.596149, "下行"],
    ["重庆科技大学西门", 106.314510, 29.591357, "下行"],
    ["大学城东路4站", 106.314295, 29.588785, "下行"],
    ["大学城东路南段", 106.313775, 29.583780, "下行"],
    ["康居西城", 106.312862, 29.580126, "下行"],
    ["康居西城花市", 106.309097, 29.579152, "下行"],
    ["康城南路", 106.306724, 29.575119, "下行"],
    ["英业达", 106.303621, 29.565268, "下行"],
    ["曾家转盘", 106.299375, 29.560237, "下行"]
]

# 上行数据
upstream_data = [
    ["曾家转盘", 106.299375, 29.560237, "上行"],
    ["英业达", 106.303813, 29.565841, "上行"],
    ["康城南路", 106.306890, 29.575142, "上行"],
    ["康居西城花市", 106.309195, 29.579435, "上行"],
    ["康居西城", 106.312448, 29.580039, "上行"],
    ["大学城东路南段", 106.314056, 29.584061, "上行"],
    ["大学城东路4站", 106.314370, 29.588717, "上行"],
    ["重庆科技大学西门", 106.314597, 29.591453, "上行"],
    ["大学城东路3站", 106.314998, 29.596327, "上行"],
    ["大学城东路", 106.315634, 29.602871, "上行"],
    ["大学城东路东", 106.316412, 29.608858, "上行"],
    ["轨道陈家桥站", 106.325296, 29.609939, "上行"],
    ["大学城北路东段路口", 106.336660, 29.609783, "上行"],
    ["陈南路口", 106.341490, 29.612107, "上行"],
    ["陈家桥新医院", 106.339617, 29.616841, "上行"]
]

def create_bus_stops_map():
    """创建公交站点地图"""
    
    # 计算地图中心点（所有站点的平均坐标）
    all_data = downstream_data + upstream_data
    center_lat = sum([point[2] for point in all_data]) / len(all_data)
    center_lon = sum([point[1] for point in all_data]) / len(all_data)
    
    # 创建地图
    m = folium.Map(
        location=[center_lat, center_lon],
        zoom_start=13,
        tiles='OpenStreetMap'
    )
    
    # 添加下行站点（蓝色）
    for i, (name, lon, lat, direction) in enumerate(downstream_data):
        folium.Marker(
            location=[lat, lon],
            popup=f"{name}<br>方向: {direction}<br>序号: {i+1}",
            tooltip=f"{name} ({direction})",
            icon=folium.Icon(color='blue', icon='arrow-down', prefix='fa')
        ).add_to(m)
    
    # 添加上行站点（红色）
    for i, (name, lon, lat, direction) in enumerate(upstream_data):
        folium.Marker(
            location=[lat, lon],
            popup=f"{name}<br>方向: {direction}<br>序号: {i+1}",
            tooltip=f"{name} ({direction})",
            icon=folium.Icon(color='red', icon='arrow-up', prefix='fa')
        ).add_to(m)
    
    # 添加下行路线（蓝色线条）
    downstream_coords = [[point[2], point[1]] for point in downstream_data]
    folium.PolyLine(
        locations=downstream_coords,
        color='blue',
        weight=3,
        opacity=0.7,
        popup='下行路线'
    ).add_to(m)
    
    # 添加上行路线（红色线条）
    upstream_coords = [[point[2], point[1]] for point in upstream_data]
    folium.PolyLine(
        locations=upstream_coords,
        color='red',
        weight=3,
        opacity=0.7,
        popup='上行路线'
    ).add_to(m)
    
    # 添加图例
    legend_html = '''
    <div style="position: fixed; 
                top: 10px; right: 10px; width: 150px; height: 90px; 
                background-color: white; border:2px solid grey; z-index:9999; 
                font-size:14px; padding: 10px">
    <p><b>公交站点图例</b></p>
    <p><i class="fa fa-arrow-down" style="color:blue"></i> 下行站点</p>
    <p><i class="fa fa-arrow-up" style="color:red"></i> 上行站点</p>
    </div>
    '''
    m.get_root().html.add_child(folium.Element(legend_html))
    
    return m

def main():
    """主函数"""
    print("正在创建公交站点地图...")
    
    # 创建地图
    map_obj = create_bus_stops_map()
    
    # 保存地图
    map_file = "bus_stops_map.html"
    map_obj.save(map_file)
    
    print(f"地图已保存为: {map_file}")
    print(f"下行站点数量: {len(downstream_data)}")
    print(f"上行站点数量: {len(upstream_data)}")
    
    # 自动打开地图
    try:
        webbrowser.open(f'file://{os.path.abspath(map_file)}')
        print("地图已在浏览器中打开")
    except Exception as e:
        print(f"无法自动打开浏览器: {e}")
        print(f"请手动打开文件: {os.path.abspath(map_file)}")

if __name__ == "__main__":
    main()
