{"cells": [{"cell_type": "code", "execution_count": null, "id": "505c39f3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from math import radians, sin, cos, sqrt, atan2\n", "import folium\n", "import glob\n", "import os\n", "from datetime import datetime, time\n", "\n", "# 1. 设置路径并获取所有txt文件\n", "base_path = '../上行数据'\n", "try:\n", "    # 获取所有txt文件路径\n", "    all_txt_files = glob.glob(os.path.join(base_path, '*.txt'))\n", "    \n", "    if all_txt_files:\n", "        print(f\"找到 {len(all_txt_files)} 个txt文件:\")\n", "        for f in all_txt_files[:5]:  # 只显示前5个文件名\n", "            print(f\"- {os.path.basename(f)}\")\n", "        if len(all_txt_files) > 5:\n", "            print(f\"... 还有 {len(all_txt_files) - 5} 个文件\")\n", "    else:\n", "        print(f\"错误：在 '{base_path}' 路径下没有找到任何txt文件。\")\n", "\n", "except Exception as e:\n", "    print(f\"错误：无法访问文件夹: {e}\")\n", "    all_txt_files = []\n", "\n", "# 定义时间段\n", "def get_time_period(time_str):\n", "    \"\"\"根据时间字符串判断属于哪个时间段\"\"\"\n", "    try:\n", "        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')\n", "        hour = dt.hour\n", "        \n", "        if 7 <= hour < 9:  # 早高峰 7:00-9:00\n", "            return 'morning_peak'\n", "        elif 10 <= hour < 16:  # 平峰 10:00-16:00\n", "            return 'midday_peak'\n", "        elif 17 <= hour < 19:  # 晚高峰 17:00-19:00\n", "            return 'evening_peak'\n", "        else:\n", "            return None\n", "    except:\n", "        return None\n", "\n", "print(\"时间段定义:\")\n", "print(\"- 早高峰: 7:00-9:00\")\n", "print(\"- 平峰: 10:00-16:00\")\n", "print(\"- 晚高峰: 17:00-19:00\")"]}, {"cell_type": "code", "execution_count": 22, "id": "0c697841", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["站点数据加载成功:\n", "   index station_name   longitude   latitude\n", "0      0       陈家桥新医院  106.339491  29.617029\n", "1      1         陈南路口  106.341308  29.611608\n", "2      2    大学城北路东段路口  106.335350  29.610001\n", "3      3       轨道陈家桥站  106.324727  29.610164\n", "4      4       大学城东路东  106.316293  29.608722\n"]}], "source": ["# 辅助函数：计算两点之间的距离（Haversine公式）\n", "def haversine(lon1, lat1, lon2, lat2):\n", "    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])\n", "    dlon = lon2 - lon1\n", "    dlat = lat2 - lat1\n", "    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2\n", "    c = 2 * atan2(sqrt(a), sqrt(1-a))\n", "    return c * 6371000 # 地球半径（米）\n", "\n", "# 辅助函数：根据速度返回颜色\n", "def speed_to_color(speed):\n", "    if speed == 0:\n", "        return 'purple' # 速度为0\n", "    elif 0 < speed <= 10:\n", "        return 'red'\n", "    elif 10 < speed <= 20:\n", "        return 'orange'\n", "    elif 20 < speed <= 30:\n", "        return 'blue'\n", "    else: # speed > 30\n", "        return 'green'\n", "\n", "# 新增：站点数据\n", "import io\n", "\n", "station_data = \"\"\"\n", "0        陈家桥新医院  106.339491  29.617029\n", "1          陈南路口  106.341308  29.611608\n", "2     大学城北路东段路口  106.335350  29.610001\n", "3        轨道陈家桥站  106.324727  29.610164\n", "4        大学城东路东  106.316293  29.608722\n", "5         大学城东路  106.315368  29.602679\n", "6       大学城东路3站  106.314804  29.596149\n", "7      重庆科技大学西门  106.314510  29.591357\n", "8       大学城东路4站  106.314295  29.588785\n", "9       大学城东路南段  106.313775  29.583780\n", "10         康居西城  106.312862  29.580126\n", "11       康居西城花市  106.309097  29.579152\n", "12         康城南路  106.306724  29.575119\n", "13          英业达  106.303621  29.565268\n", "14         曾家转盘  106.299375  29.560237\n", "\"\"\"\n", "\n", "stations_df = pd.read_csv(io.StringIO(station_data), sep=r'\\s+', header=None,\n", "                          names=['index', 'station_name', 'longitude', 'latitude'])\n", "print(\"站点数据加载成功:\")\n", "print(stations_df.head())"]}, {"cell_type": "code", "execution_count": 23, "id": "45a56e73", "metadata": {}, "outputs": [], "source": ["# 核心处理函数：加载、清洗、处理数据并生成地图\n", "def process_and_generate_map(file_paths, output_filename):\n", "    print(f\"\\n--- 开始处理并生成地图: {output_filename} ---\")\n", "    \n", "    # 1. 加载数据\n", "    li = []\n", "    for filename in file_paths:\n", "        if not os.path.exists(filename):\n", "            print(f\"警告：文件不存在，跳过: {filename}\")\n", "            continue\n", "        vehicle_id = os.path.basename(os.path.dirname(filename))\n", "        try:\n", "            df_file = pd.read_csv(filename, index_col=None, header=0)\n", "            if not df_file.empty:\n", "                df_file['vehicle_id'] = vehicle_id\n", "                li.append(df_file)\n", "        except Exception as e:\n", "            print(f\"读取文件 {filename} 时出错: {e}\")\n", "\n", "    if not li:\n", "        print(f\"错误：未能从指定的路径加载任何数据。请检查文件是否存在于所选车辆文件夹中。\")\n", "        return\n", "\n", "    df = pd.concat(li, axis=0, ignore_index=True)\n", "    print(f\"从 {len(li)} 个文件中加载了 {len(df)} 行数据。\")\n", "    print(\"处理的车辆ID:\", df['vehicle_id'].unique())\n", "\n", "    # 2. 剔除异常点\n", "    Q1 = df[['Longitude', 'Latitude']].quantile(0.25)\n", "    Q3 = df[['Longitude', 'Latitude']].quantile(0.75)\n", "    IQR = Q3 - Q1\n", "    df_no_outliers = df[~((df[['Longitude', 'Latitude']] < (Q1 - 1.5 * IQR)) |(df[['Longitude', 'Latitude']] > (Q3 + 1.5 * IQR))).any(axis=1)]\n", "    print(f\"原始数据量: {len(df)}\")\n", "    print(f\"剔除异常点后数据量: {len(df_no_outliers)}\")\n", "    if df_no_outliers.empty:\n", "        print(\"剔除异常点后没有剩余数据，无法生成地图。\")\n", "        return\n", "\n", "    # 3. 划分路段并处理长时间停车\n", "    all_segments = []\n", "    all_stationary_points = []\n", "    all_short_stationary_points = []\n", "    grouped = df_no_outliers.groupby('vehicle_id')\n", "\n", "    for vehicle_id, vehicle_df in grouped:\n", "        segments = []\n", "        stationary_points = []\n", "        short_stationary_points = []\n", "        start_index = 0\n", "        zero_speed_count = 0\n", "        vehicle_df = vehicle_df.reset_index(drop=True)\n", "\n", "        MAX_ADJACENT_DISTANCE = 500 \n", "\n", "        for i in range(1, len(vehicle_df)):\n", "            lon1, lat1 = vehicle_df.iloc[i-1]['Longitude'], vehicle_df.iloc[i-1]['Latitude']\n", "            lon2, lat2 = vehicle_df.iloc[i]['Longitude'], vehicle_df.iloc[i]['Latitude']\n", "            \n", "            adjacent_distance = haversine(lon1, lat1, lon2, lat2)\n", "\n", "            if adjacent_distance > MAX_ADJACENT_DISTANCE:\n", "                if start_index < i:\n", "                    segments.append(vehicle_df.iloc[start_index:i])\n", "                start_index = i\n", "                zero_speed_count = 0\n", "                continue\n", "\n", "            is_same_location = (vehicle_df.iloc[i]['Longitude'] == vehicle_df.iloc[i-1]['Longitude'] and \n", "                                vehicle_df.iloc[i]['Latitude'] == vehicle_df.iloc[i-1]['Latitude'])\n", "\n", "            if vehicle_df.iloc[i]['GpsSpeed'] == 0 and is_same_location:\n", "                zero_speed_count += 1\n", "            else:\n", "                if zero_speed_count > 6: # 超过6个点（1分钟）认为是长时间停留\n", "                    stationary_points.append(vehicle_df.iloc[i - zero_speed_count])\n", "                    if start_index < i - zero_speed_count:\n", "                         segments.append(vehicle_df.iloc[start_index:i-zero_speed_count])\n", "                    start_index = i \n", "                elif 3 <= zero_speed_count <= 6: # 3到6个点认为是短时间停留\n", "                    short_stationary_points.append(vehicle_df.iloc[i - zero_speed_count])\n", "                    if start_index < i - zero_speed_count:\n", "                        segments.append(vehicle_df.iloc[start_index:i-zero_speed_count])\n", "                    start_index = i\n", "                zero_speed_count = 0\n", "\n", "            if start_index < i:\n", "                distance = haversine(vehicle_df.iloc[start_index]['Longitude'], vehicle_df.iloc[start_index]['Latitude'],\n", "                                     vehicle_df.iloc[i]['Longitude'], vehicle_df.iloc[i]['Latitude'])\n", "                if distance >= 100:\n", "                    segments.append(vehicle_df.iloc[start_index:i+1])\n", "                    start_index = i + 1\n", "\n", "        if start_index < len(vehicle_df):\n", "            segments.append(vehicle_df.iloc[start_index:])\n", "        \n", "        all_segments.extend(segments)\n", "        all_stationary_points.extend(stationary_points)\n", "        all_short_stationary_points.extend(short_stationary_points)\n", "\n", "    print(f\"所有车辆的线路总共被划分为 {len(all_segments)} 个路段\")\n", "    print(f\"所有车辆总共检测到 {len(all_stationary_points)} 个长时间停留点\")\n", "    print(f\"所有车辆总共检测到 {len(all_short_stationary_points)} 个短时间停留点\")\n", "\n", "    # 4. 计算每个路段的速度平均值\n", "    segment_avg_speeds = [segment['GpsSpeed'].mean() for segment in all_segments if not segment.empty]\n", "\n", "    # 5. 使用folium在地图上可视化\n", "    center_lon = df_no_outliers['Longitude'].mean()\n", "    center_lat = df_no_outliers['Latitude'].mean()\n", "    m = folium.Map(location=[center_lat, center_lon], zoom_start=12)\n", "\n", "    # 新增：在地图上标记站点\n", "    if 'stations_df' in globals() and not stations_df.empty:\n", "        for idx, row in stations_df.iterrows():\n", "            folium.Marker(\n", "                location=[row['latitude'], row['longitude']],\n", "                popup=f\"{row['station_name']}\",\n", "                icon=folium.Icon(color='blue', icon='bus', prefix='fa')\n", "            ).add_to(m)\n", "        print(f\"已在地图上标记 {len(stations_df)} 个站点。\")\n", "\n", "    # 绘制路段\n", "    for i, segment in enumerate(all_segments):\n", "        if not segment.empty and i < len(segment_avg_speeds):\n", "            avg_speed = segment_avg_speeds[i]\n", "            color = speed_to_color(avg_speed)\n", "            points = segment[['Latitude', 'Longitude']].values.tolist()\n", "            folium.PolyLine(points, color=color, weight=5, opacity=0.2, popup=f'平均速度: {avg_speed:.2f} km/h').add_to(m)\n", "\n", "    # 标记短时间停留点\n", "    for point in all_short_stationary_points:\n", "        folium.CircleMarker(\n", "            location=[point['Latitude'], point['Longitude']],\n", "            radius=3, color='gray', fill=True, fill_color='gray', fill_opacity=0.1, popup='短时间停留'\n", "        ).add_to(m)\n", "\n", "    # 标记长时间停留点\n", "    for point in all_stationary_points:\n", "        folium.CircleMarker(\n", "            location=[point['Latitude'], point['Longitude']],\n", "            radius=3, color='black', fill=True, fill_color='black', fill_opacity=0.5, popup='长时间停留'\n", "        ).add_to(m)\n", "\n", "    # 添加图例\n", "    legend_html = '''\n", "    <div style=\"position: fixed; \n", "         bottom: 50px; left: 50px; width: 220px; height: 170px; \n", "         border:2px solid grey; z-index:9999; font-size:14px;\n", "         background-color:white;\n", "         \">&nbsp;<b>速度图例 (km/h)</b><br>\n", "         &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:red\"></i>&nbsp;0 &lt; 速度 &le; 10<br>\n", "         &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:orange\"></i>&nbsp;10 &lt; 速度 &le; 20<br>\n", "         &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:blue\"></i>&nbsp;20 &lt; 速度 &le; 30<br>\n", "         &nbsp;<i class=\"fa fa-minus fa-1x\" style=\"color:green\"></i>&nbsp;速度 &gt; 30<br>\n", "         &nbsp;<i class=\"fa fa-circle fa-1x\" style=\"color:black\"></i>&nbsp;长时间停留点<br>\n", "         &nbsp;<i class=\"fa fa-circle fa-1x\" style=\"color:gray\"></i>&nbsp;短时间停留点\n", "    </div>\n", "    '''\n", "    m.get_root().html.add_child(folium.Element(legend_html))\n", "\n", "    # 保存地图\n", "    m.save(output_filename)\n", "    print(f\"地图已成功保存至: {output_filename}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "d3fda38d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 开始处理并生成地图: combined_morning_peak_map.html ---\n", "从 4 个文件中加载了 75439 行数据。\n", "处理的车辆ID: ['car_23494' 'car_23493' 'car_23486' 'car_23492']\n", "原始数据量: 75439\n", "剔除异常点后数据量: 74059\n", "所有车辆的线路总共被划分为 14260 个路段\n", "所有车辆总共检测到 849 个长时间停留点\n", "所有车辆总共检测到 1876 个短时间停留点\n", "已在地图上标记 15 个站点。\n", "地图已成功保存至: combined_morning_peak_map.html\n", "\n", "--- 开始处理并生成地图: combined_midday_peak_map.html ---\n", "从 4 个文件中加载了 75603 行数据。\n", "处理的车辆ID: ['car_23494' 'car_23493' 'car_23486' 'car_23492']\n", "原始数据量: 75603\n", "剔除异常点后数据量: 69254\n", "所有车辆的线路总共被划分为 9013 个路段\n", "所有车辆总共检测到 459 个长时间停留点\n", "所有车辆总共检测到 1076 个短时间停留点\n", "已在地图上标记 15 个站点。\n", "地图已成功保存至: combined_midday_peak_map.html\n", "\n", "--- 开始处理并生成地图: combined_evening_peak_map.html ---\n", "从 4 个文件中加载了 75250 行数据。\n", "处理的车辆ID: ['car_23494' 'car_23493' 'car_23486' 'car_23492']\n", "原始数据量: 75250\n", "剔除异常点后数据量: 74563\n", "所有车辆的线路总共被划分为 14283 个路段\n", "所有车辆总共检测到 913 个长时间停留点\n", "所有车辆总共检测到 1700 个短时间停留点\n", "已在地图上标记 15 个站点。\n", "地图已成功保存至: combined_evening_peak_map.html\n"]}], "source": ["# 循环处理不同高峰时段的数据并生成合并地图\n", "if selected_vehicle_dirs:\n", "    for peak_file in peak_files:\n", "        peak_name = peak_file.split('.')[0] # e.g., 'morning_peak'\n", "        output_map_name = f\"combined_{peak_name}_map.html\"\n", "        \n", "        # 构建当前高峰时段需要处理的文件列表\n", "        files_to_process = [os.path.join(vehicle_dir, peak_file) for vehicle_dir in selected_vehicle_dirs]\n", "        \n", "        # 调用核心函数处理数据并生成地图\n", "        process_and_generate_map(files_to_process, output_map_name)\n", "else:\n", "    print(\"没有选择任何车辆，无法继续处理。\")"]}, {"cell_type": "code", "execution_count": 25, "id": "e84b7e2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["所有高峰时段处理完毕。\n"]}], "source": ["print(\"所有高峰时段处理完毕。\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}