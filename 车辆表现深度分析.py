import os
import csv
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.font_manager as fm
from datetime import datetime, date
from collections import defaultdict
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体"""
    chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"使用字体: {font}")
            return True
    return False

def analyze_daily_vehicle_performance(vehicle_file_path):
    """分析单个车辆每天的表现"""
    car_num = os.path.basename(vehicle_file_path).replace('CarNum_', '').replace('.txt', '')
    print(f"  分析车辆: {car_num}")

    # 读取数据
    data = []
    with open(vehicle_file_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过标题行
        for row in reader:
            if len(row) < 5:
                continue
            try:
                gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                speed = float(row[4])
                data.append((gps_time, speed))
            except ValueError:
                continue

    if not data:
        return []

    # 按日期分组
    daily_data = defaultdict(list)
    for gps_time, speed in data:
        date_key = gps_time.date()
        daily_data[date_key].append((gps_time, speed))

    daily_analyses = []

    # 分析每天的数据
    for analysis_date, day_data in daily_data.items():
        if len(day_data) < 10:  # 数据点太少
            continue

        speeds = [speed for _, speed in day_data]

        # 基础速度指标
        avg_speed = np.mean(speeds)
        max_speed = max(speeds)
        min_speed = min(speeds)
        speed_std = np.std(speeds)  # 车速波动（标准差）
        speed_range = max_speed - min_speed

        # 速度变化分析
        speed_changes = []
        for i in range(1, len(speeds)):
            speed_changes.append(abs(speeds[i] - speeds[i-1]))

        avg_speed_change = np.mean(speed_changes) if speed_changes else 0
        max_speed_change = max(speed_changes) if speed_changes else 0

        # 停车分析
        stop_count = 0
        total_stop_time = 0
        stop_durations = []

        i = 0
        while i < len(day_data):
            gps_time, speed = day_data[i]

            if speed <= 5.0:  # 认为是停车
                stop_start = gps_time
                j = i
                while j < len(day_data) and day_data[j][1] <= 5.0:
                    j += 1

                if j > i:
                    stop_end = day_data[j-1][0]
                    stop_duration = (stop_end - stop_start).total_seconds()

                    if stop_duration >= 3:  # 停车超过3秒才算
                        stop_count += 1
                        total_stop_time += stop_duration
                        stop_durations.append(stop_duration)

                i = j
            else:
                i += 1

        # 速度分布分析
        low_speed_ratio = sum(1 for s in speeds if s <= 10) / len(speeds)
        high_speed_ratio = sum(1 for s in speeds if s >= 30) / len(speeds)
        zero_speed_ratio = sum(1 for s in speeds if s == 0) / len(speeds)

        # 驾驶平稳性指标
        speed_volatility = np.std(speed_changes) if len(speed_changes) > 1 else 0

        # 急加速和急减速
        rapid_changes = sum(1 for change in speed_changes if change > 15)

        daily_analysis = {
            'car_num': car_num,
            'date': analysis_date,
            'data_points': len(day_data),

            # 核心指标
            'avg_speed': avg_speed,
            'max_speed': max_speed,
            'min_speed': min_speed,
            'speed_std': speed_std,  # 车速波动
            'stop_count': stop_count,  # 停车次数

            # 扩展指标
            'speed_range': speed_range,
            'avg_speed_change': avg_speed_change,
            'max_speed_change': max_speed_change,
            'speed_volatility': speed_volatility,
            'rapid_changes': rapid_changes,

            # 停车相关
            'total_stop_time': total_stop_time,
            'avg_stop_time': np.mean(stop_durations) if stop_durations else 0,
            'max_stop_time': max(stop_durations) if stop_durations else 0,

            # 速度分布
            'low_speed_ratio': low_speed_ratio,
            'high_speed_ratio': high_speed_ratio,
            'zero_speed_ratio': zero_speed_ratio,
        }

        daily_analyses.append(daily_analysis)

    return daily_analyses

def calculate_vehicle_summary_stats(all_daily_data):
    """计算每辆车的汇总统计"""
    vehicle_summaries = {}

    # 按车辆分组
    vehicle_groups = defaultdict(list)
    for daily_data in all_daily_data:
        vehicle_groups[daily_data['car_num']].append(daily_data)

    for car_num, daily_records in vehicle_groups.items():
        if len(daily_records) < 5:  # 至少需要5天数据
            continue

        # 计算各项指标的统计值
        summary = {
            'car_num': car_num,
            'total_days': len(daily_records),

            # 平均速度统计
            'avg_speed_mean': np.mean([r['avg_speed'] for r in daily_records]),
            'avg_speed_std': np.std([r['avg_speed'] for r in daily_records]),
            'avg_speed_cv': np.std([r['avg_speed'] for r in daily_records]) / np.mean([r['avg_speed'] for r in daily_records]),

            # 最高速度统计
            'max_speed_mean': np.mean([r['max_speed'] for r in daily_records]),
            'max_speed_std': np.std([r['max_speed'] for r in daily_records]),
            'max_speed_cv': np.std([r['max_speed'] for r in daily_records]) / np.mean([r['max_speed'] for r in daily_records]),

            # 车速波动统计
            'speed_std_mean': np.mean([r['speed_std'] for r in daily_records]),
            'speed_std_std': np.std([r['speed_std'] for r in daily_records]),
            'speed_std_cv': np.std([r['speed_std'] for r in daily_records]) / np.mean([r['speed_std'] for r in daily_records]),

            # 停车次数统计
            'stop_count_mean': np.mean([r['stop_count'] for r in daily_records]),
            'stop_count_std': np.std([r['stop_count'] for r in daily_records]),
            'stop_count_cv': np.std([r['stop_count'] for r in daily_records]) / np.mean([r['stop_count'] for r in daily_records]),

            # 其他重要指标
            'speed_volatility_mean': np.mean([r['speed_volatility'] for r in daily_records]),
            'rapid_changes_mean': np.mean([r['rapid_changes'] for r in daily_records]),
            'total_stop_time_mean': np.mean([r['total_stop_time'] for r in daily_records]),
            'low_speed_ratio_mean': np.mean([r['low_speed_ratio'] for r in daily_records]),
            'high_speed_ratio_mean': np.mean([r['high_speed_ratio'] for r in daily_records]),

            # 一致性指标（变异系数的平均值）
            'consistency_score': 1 / (1 + (
                np.std([r['avg_speed'] for r in daily_records]) / np.mean([r['avg_speed'] for r in daily_records]) +
                np.std([r['speed_std'] for r in daily_records]) / np.mean([r['speed_std'] for r in daily_records]) +
                np.std([r['stop_count'] for r in daily_records]) / np.mean([r['stop_count'] for r in daily_records])
            ) / 3)
        }

        vehicle_summaries[car_num] = summary

    return vehicle_summaries

def perform_vehicle_clustering(vehicle_summaries):
    """对车辆进行聚类分析"""
    print("正在进行车辆聚类分析...")

    # 转换为DataFrame
    df = pd.DataFrame(list(vehicle_summaries.values()))

    # 选择聚类特征（重点关注题目要求的指标）
    clustering_features = [
        'avg_speed_mean',      # 平均速度
        'max_speed_mean',      # 最高速度
        'speed_std_mean',      # 车速波动
        'stop_count_mean',     # 停车次数
        'speed_volatility_mean', # 速度变化波动性
        'rapid_changes_mean',   # 急速变化次数
        'consistency_score'     # 一致性评分
    ]

    # 确保所有特征都存在
    available_features = [f for f in clustering_features if f in df.columns]
    print(f"使用聚类特征: {available_features}")

    if len(available_features) < 4:
        print("可用特征太少，无法进行聚类")
        return df, None

    # 准备聚类数据
    X = df[available_features].fillna(0)

    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # K-means聚类（3个类别：保守、正常、激进）
    kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
    clusters = kmeans.fit_predict(X_scaled)

    # 添加聚类结果
    df['cluster'] = clusters

    # 分析每个聚类的特征
    cluster_analysis = {}

    for cluster_id in range(3):
        cluster_data = df[df['cluster'] == cluster_id]

        analysis = {
            'vehicle_count': len(cluster_data),
            'vehicles': list(cluster_data['car_num']),
            'avg_speed': cluster_data['avg_speed_mean'].mean(),
            'max_speed': cluster_data['max_speed_mean'].mean(),
            'speed_volatility': cluster_data['speed_std_mean'].mean(),
            'stop_frequency': cluster_data['stop_count_mean'].mean(),
            'consistency': cluster_data['consistency_score'].mean(),
            'rapid_changes': cluster_data['rapid_changes_mean'].mean()
        }

        cluster_analysis[cluster_id] = analysis

    # 根据特征给聚类命名
    cluster_names = {0: '未分类', 1: '未分类', 2: '未分类'}

    # 计算综合评分来区分三种类型
    avg_speeds = [cluster_analysis[i]['avg_speed'] for i in range(3)]
    stop_frequencies = [cluster_analysis[i]['stop_frequency'] for i in range(3)]
    speed_volatilities = [cluster_analysis[i]['speed_volatility'] for i in range(3)]

    # 激进型：高速度、低停车频率
    aggressive_scores = []
    for i in range(3):
        score = (avg_speeds[i] / max(avg_speeds)) - (stop_frequencies[i] / max(stop_frequencies))
        aggressive_scores.append(score)

    # 保守型：低速度、高停车频率
    conservative_scores = []
    for i in range(3):
        score = -(avg_speeds[i] / max(avg_speeds)) + (stop_frequencies[i] / max(stop_frequencies))
        conservative_scores.append(score)

    # 分配类型名称
    aggressive_cluster = aggressive_scores.index(max(aggressive_scores))
    cluster_names[aggressive_cluster] = '激进型'

    conservative_cluster = conservative_scores.index(max(conservative_scores))
    cluster_names[conservative_cluster] = '保守型'

    # 剩下的是正常型
    for i in range(3):
        if cluster_names[i] == '未分类':
            cluster_names[i] = '正常型'

    # 更新聚类分析结果
    for cluster_id in range(3):
        cluster_analysis[cluster_id]['style_name'] = cluster_names[cluster_id]

    # 在DataFrame中添加风格名称
    df['driving_style'] = df['cluster'].map(cluster_names)

    return df, cluster_analysis

def create_comprehensive_visualizations(df, cluster_analysis, all_daily_data):
    """创建综合可视化分析"""
    has_chinese_font = setup_chinese_font()

    # 1. 驾驶风格分布和关键指标对比
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # 1.1 驾驶风格分布饼图
    style_counts = df['driving_style'].value_counts()
    colors = ['#ff9999', '#66b3ff', '#99ff99']
    axes[0, 0].pie(style_counts.values, labels=style_counts.index, autopct='%1.1f%%', colors=colors)
    title1 = '驾驶风格分布' if has_chinese_font else 'Driving Style Distribution'
    axes[0, 0].set_title(title1, fontsize=12, fontweight='bold')

    # 1.2 平均速度对比
    styles = list(cluster_analysis.keys())
    avg_speeds = [cluster_analysis[i]['avg_speed'] for i in styles]
    style_names = [cluster_analysis[i]['style_name'] for i in styles]

    bars = axes[0, 1].bar(style_names, avg_speeds, color=colors)
    title2 = '平均速度对比' if has_chinese_font else 'Average Speed Comparison'
    axes[0, 1].set_title(title2, fontsize=12)
    ylabel2 = '平均速度 (km/h)' if has_chinese_font else 'Average Speed (km/h)'
    axes[0, 1].set_ylabel(ylabel2)

    for bar, speed in zip(bars, avg_speeds):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                       f'{speed:.1f}', ha='center', va='bottom')

    # 1.3 最高速度对比
    max_speeds = [cluster_analysis[i]['max_speed'] for i in styles]
    bars = axes[0, 2].bar(style_names, max_speeds, color=colors)
    title3 = '最高速度对比' if has_chinese_font else 'Maximum Speed Comparison'
    axes[0, 2].set_title(title3, fontsize=12)
    ylabel3 = '最高速度 (km/h)' if has_chinese_font else 'Maximum Speed (km/h)'
    axes[0, 2].set_ylabel(ylabel3)

    for bar, speed in zip(bars, max_speeds):
        axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                       f'{speed:.1f}', ha='center', va='bottom')

    # 1.4 车速波动对比
    speed_volatilities = [cluster_analysis[i]['speed_volatility'] for i in styles]
    bars = axes[1, 0].bar(style_names, speed_volatilities, color=colors)
    title4 = '车速波动对比' if has_chinese_font else 'Speed Volatility Comparison'
    axes[1, 0].set_title(title4, fontsize=12)
    ylabel4 = '车速波动' if has_chinese_font else 'Speed Volatility'
    axes[1, 0].set_ylabel(ylabel4)

    for bar, vol in zip(bars, speed_volatilities):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                       f'{vol:.1f}', ha='center', va='bottom')

    # 1.5 停车次数对比
    stop_frequencies = [cluster_analysis[i]['stop_frequency'] for i in styles]
    bars = axes[1, 1].bar(style_names, stop_frequencies, color=colors)
    title5 = '停车次数对比' if has_chinese_font else 'Stop Frequency Comparison'
    axes[1, 1].set_title(title5, fontsize=12)
    ylabel5 = '平均停车次数' if has_chinese_font else 'Average Stop Count'
    axes[1, 1].set_ylabel(ylabel5)

    for bar, freq in zip(bars, stop_frequencies):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                       f'{freq:.1f}', ha='center', va='bottom')

    # 1.6 一致性评分对比
    consistencies = [cluster_analysis[i]['consistency'] for i in styles]
    bars = axes[1, 2].bar(style_names, consistencies, color=colors)
    title6 = '驾驶一致性对比' if has_chinese_font else 'Driving Consistency Comparison'
    axes[1, 2].set_title(title6, fontsize=12)
    ylabel6 = '一致性评分' if has_chinese_font else 'Consistency Score'
    axes[1, 2].set_ylabel(ylabel6)

    for bar, cons in zip(bars, consistencies):
        axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{cons:.3f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig('车辆驾驶风格综合分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 车辆表现原始数值热力图
    plt.figure(figsize=(20, 12))

    # 选择关键指标及其中文标签
    heatmap_metrics = {
        'avg_speed_mean': '平均速度(km/h)',
        'max_speed_mean': '最高速度(km/h)',
        'speed_std_mean': '车速波动',
        'stop_count_mean': '停车次数',
        'speed_volatility_mean': '速度变化波动',
        'rapid_changes_mean': '急速变化次数',
        'total_stop_time_mean': '总停车时间(秒)',
        'consistency_score': '一致性评分'
    }

    # 按车辆编号排序
    df_sorted = df.sort_values('car_num')

    # 创建子图，每个指标一个热力图
    fig, axes = plt.subplots(len(heatmap_metrics), 1, figsize=(16, 3*len(heatmap_metrics)))

    for idx, (metric, label) in enumerate(heatmap_metrics.items()):
        if metric in df_sorted.columns:
            # 准备数据 - 每辆车一行，只有一个指标值
            data_for_heatmap = df_sorted[metric].values.reshape(1, -1)
            vehicle_labels = df_sorted['car_num'].astype(str).tolist()

            # 创建热力图
            im = axes[idx].imshow(data_for_heatmap, cmap='RdYlBu_r', aspect='auto')

            # 设置标签
            axes[idx].set_xticks(range(len(vehicle_labels)))
            axes[idx].set_xticklabels(vehicle_labels)
            axes[idx].set_yticks([0])
            axes[idx].set_yticklabels([label])

            # 在每个格子中显示数值
            for j, value in enumerate(df_sorted[metric]):
                if metric == 'consistency_score':
                    text = f'{value:.3f}'
                elif 'time' in metric:
                    text = f'{value:.0f}'
                else:
                    text = f'{value:.1f}'
                axes[idx].text(j, 0, text, ha='center', va='center',
                             color='white' if value > df_sorted[metric].median() else 'black',
                             fontweight='bold', fontsize=10)

            # 添加颜色条
            cbar = plt.colorbar(im, ax=axes[idx], orientation='horizontal', pad=0.1)
            cbar.set_label(label, fontsize=10)

            # 设置标题
            axes[idx].set_title(f'{label} - 原始数值', fontsize=12, fontweight='bold')

    plt.tight_layout()
    plt.savefig('车辆表现原始数值热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 创建一个综合的原始数值表格热力图
    plt.figure(figsize=(18, 10))

    # 选择要显示的指标
    display_metrics = ['avg_speed_mean', 'max_speed_mean', 'speed_std_mean',
                      'stop_count_mean', 'speed_volatility_mean', 'consistency_score']

    # 重命名列标签
    metric_labels = {
        'avg_speed_mean': '平均速度\n(km/h)',
        'max_speed_mean': '最高速度\n(km/h)',
        'speed_std_mean': '车速波动',
        'stop_count_mean': '停车次数',
        'speed_volatility_mean': '速度变化\n波动',
        'consistency_score': '一致性\n评分'
    }

    # 准备数据
    heatmap_data = df_sorted.set_index('car_num')[display_metrics]
    heatmap_data.columns = [metric_labels[col] for col in heatmap_data.columns]

    # 创建热力图，但不进行标准化
    # 为了更好的视觉效果，我们为每列单独设置颜色映射
    fig, ax = plt.subplots(figsize=(14, 8))

    # 创建注释矩阵
    annot_data = heatmap_data.copy()
    for col in annot_data.columns:
        if '一致性' in col:
            annot_data[col] = annot_data[col].apply(lambda x: f'{x:.3f}')
        elif '时间' in col:
            annot_data[col] = annot_data[col].apply(lambda x: f'{x:.0f}')
        else:
            annot_data[col] = annot_data[col].apply(lambda x: f'{x:.1f}')

    # 使用seaborn创建热力图，但保持原始数值
    sns.heatmap(heatmap_data.T, annot=annot_data.T, fmt='', cmap='RdYlBu_r',
                cbar_kws={'label': '数值大小（各指标保持原始单位）'},
                linewidths=0.5, square=False)

    title_heatmap = '各车辆关键指标原始数值热力图' if has_chinese_font else 'Vehicle Key Performance Indicators (Original Values)'
    plt.title(title_heatmap, fontsize=16, fontweight='bold', pad=20)
    xlabel_heatmap = '车辆编号' if has_chinese_font else 'Vehicle Number'
    plt.xlabel(xlabel_heatmap, fontsize=12)
    ylabel_heatmap = '性能指标' if has_chinese_font else 'Performance Indicators'
    plt.ylabel(ylabel_heatmap, fontsize=12)

    plt.xticks(rotation=0)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('车辆综合表现原始数值热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 每日表现变化趋势图
    plt.figure(figsize=(15, 10))

    # 转换每日数据为DataFrame
    daily_df = pd.DataFrame(all_daily_data)
    daily_df['date'] = pd.to_datetime(daily_df['date'])

    # 为每辆车分配颜色（按驾驶风格）
    vehicle_colors = {}
    style_color_map = {'激进型': 'red', '正常型': 'blue', '保守型': 'green'}

    for _, row in df.iterrows():
        vehicle_colors[row['car_num']] = style_color_map.get(row['driving_style'], 'gray')

    # 绘制平均速度趋势
    plt.subplot(2, 2, 1)
    for car_num in df['car_num'].unique():
        car_daily_data = daily_df[daily_df['car_num'] == car_num]
        if len(car_daily_data) > 1:
            plt.plot(car_daily_data['date'], car_daily_data['avg_speed'],
                    color=vehicle_colors.get(car_num, 'gray'), alpha=0.7, label=f'车辆{car_num}')

    title_trend1 = '每日平均速度趋势' if has_chinese_font else 'Daily Average Speed Trend'
    plt.title(title_trend1)
    ylabel_trend1 = '平均速度 (km/h)' if has_chinese_font else 'Average Speed (km/h)'
    plt.ylabel(ylabel_trend1)
    plt.xticks(rotation=45)

    # 绘制停车次数趋势
    plt.subplot(2, 2, 2)
    for car_num in df['car_num'].unique():
        car_daily_data = daily_df[daily_df['car_num'] == car_num]
        if len(car_daily_data) > 1:
            plt.plot(car_daily_data['date'], car_daily_data['stop_count'],
                    color=vehicle_colors.get(car_num, 'gray'), alpha=0.7)

    title_trend2 = '每日停车次数趋势' if has_chinese_font else 'Daily Stop Count Trend'
    plt.title(title_trend2)
    ylabel_trend2 = '停车次数' if has_chinese_font else 'Stop Count'
    plt.ylabel(ylabel_trend2)
    plt.xticks(rotation=45)

    # 绘制车速波动趋势
    plt.subplot(2, 2, 3)
    for car_num in df['car_num'].unique():
        car_daily_data = daily_df[daily_df['car_num'] == car_num]
        if len(car_daily_data) > 1:
            plt.plot(car_daily_data['date'], car_daily_data['speed_std'],
                    color=vehicle_colors.get(car_num, 'gray'), alpha=0.7)

    title_trend3 = '每日车速波动趋势' if has_chinese_font else 'Daily Speed Volatility Trend'
    plt.title(title_trend3)
    ylabel_trend3 = '车速波动' if has_chinese_font else 'Speed Volatility'
    plt.ylabel(ylabel_trend3)
    plt.xticks(rotation=45)

    # 绘制最高速度趋势
    plt.subplot(2, 2, 4)
    for car_num in df['car_num'].unique():
        car_daily_data = daily_df[daily_df['car_num'] == car_num]
        if len(car_daily_data) > 1:
            plt.plot(car_daily_data['date'], car_daily_data['max_speed'],
                    color=vehicle_colors.get(car_num, 'gray'), alpha=0.7)

    title_trend4 = '每日最高速度趋势' if has_chinese_font else 'Daily Maximum Speed Trend'
    plt.title(title_trend4)
    ylabel_trend4 = '最高速度 (km/h)' if has_chinese_font else 'Maximum Speed (km/h)'
    plt.ylabel(ylabel_trend4)
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig('车辆每日表现趋势图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_original_value_heatmaps(vehicle_summaries, all_daily_data):
    """创建原始数值热力图"""
    has_chinese_font = setup_chinese_font()

    # 转换为DataFrame
    df = pd.DataFrame(list(vehicle_summaries.values()))
    df = df.sort_values('car_num')

    # 1. 创建一个综合的原始数值表格热力图
    plt.figure(figsize=(18, 10))

    # 选择要显示的指标
    display_metrics = ['avg_speed_mean', 'max_speed_mean', 'speed_std_mean',
                      'stop_count_mean', 'speed_volatility_mean', 'consistency_score']

    # 重命名列标签
    metric_labels = {
        'avg_speed_mean': '平均速度\n(km/h)',
        'max_speed_mean': '最高速度\n(km/h)',
        'speed_std_mean': '车速波动',
        'stop_count_mean': '停车次数',
        'speed_volatility_mean': '速度变化\n波动',
        'consistency_score': '一致性\n评分'
    }

    # 准备数据
    heatmap_data = df.set_index('car_num')[display_metrics]
    heatmap_data.columns = [metric_labels[col] for col in heatmap_data.columns]

    # 创建注释矩阵
    annot_data = heatmap_data.copy()
    for col in annot_data.columns:
        if '一致性' in col:
            annot_data[col] = annot_data[col].apply(lambda x: f'{x:.3f}')
        elif '时间' in col:
            annot_data[col] = annot_data[col].apply(lambda x: f'{x:.0f}')
        else:
            annot_data[col] = annot_data[col].apply(lambda x: f'{x:.1f}')

    # 使用seaborn创建热力图，但保持原始数值
    sns.heatmap(heatmap_data.T, annot=annot_data.T, fmt='', cmap='RdYlBu_r',
                cbar_kws={'label': '数值大小（各指标保持原始单位）'},
                linewidths=0.5, square=False)

    title_heatmap = '各车辆关键指标原始数值热力图' if has_chinese_font else 'Vehicle Key Performance Indicators (Original Values)'
    plt.title(title_heatmap, fontsize=16, fontweight='bold', pad=20)
    xlabel_heatmap = '车辆编号' if has_chinese_font else 'Vehicle Number'
    plt.xlabel(xlabel_heatmap, fontsize=12)
    ylabel_heatmap = '性能指标' if has_chinese_font else 'Performance Indicators'
    plt.ylabel(ylabel_heatmap, fontsize=12)

    plt.xticks(rotation=0)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('车辆综合表现原始数值热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 创建详细的统计表格
    print("\n=== 车辆表现详细统计 ===")
    print("车辆编号 | 平均速度(km/h) | 最高速度(km/h) | 车速波动 | 停车次数 | 速度变化波动 | 一致性评分")
    print("-" * 100)

    for _, row in df.iterrows():
        print(f"{row['car_num']:8s} | {row['avg_speed_mean']:11.1f} | {row['max_speed_mean']:11.1f} | "
              f"{row['speed_std_mean']:8.1f} | {row['stop_count_mean']:8.1f} | "
              f"{row['speed_volatility_mean']:10.1f} | {row['consistency_score']:8.3f}")

    # 3. 创建每个指标的排名
    print("\n=== 各指标排名 ===")

    print("\n🏃 平均速度排名（从高到低）:")
    speed_ranking = df.sort_values('avg_speed_mean', ascending=False)
    for i, (_, row) in enumerate(speed_ranking.iterrows(), 1):
        print(f"  {i:2d}. 车辆{row['car_num']}: {row['avg_speed_mean']:.1f} km/h")

    print("\n⚡ 最高速度排名（从高到低）:")
    max_speed_ranking = df.sort_values('max_speed_mean', ascending=False)
    for i, (_, row) in enumerate(max_speed_ranking.iterrows(), 1):
        print(f"  {i:2d}. 车辆{row['car_num']}: {row['max_speed_mean']:.1f} km/h")

    print("\n📊 车速波动排名（从低到高，越低越稳定）:")
    volatility_ranking = df.sort_values('speed_std_mean', ascending=True)
    for i, (_, row) in enumerate(volatility_ranking.iterrows(), 1):
        print(f"  {i:2d}. 车辆{row['car_num']}: {row['speed_std_mean']:.1f}")

    print("\n🛑 停车次数排名（从少到多）:")
    stop_ranking = df.sort_values('stop_count_mean', ascending=True)
    for i, (_, row) in enumerate(stop_ranking.iterrows(), 1):
        print(f"  {i:2d}. 车辆{row['car_num']}: {row['stop_count_mean']:.1f} 次/天")

    print("\n🎯 一致性评分排名（从高到低，越高越一致）:")
    consistency_ranking = df.sort_values('consistency_score', ascending=False)
    for i, (_, row) in enumerate(consistency_ranking.iterrows(), 1):
        print(f"  {i:2d}. 车辆{row['car_num']}: {row['consistency_score']:.3f}")

def main():
    """主分析流程"""
    print("=== 车辆驾驶表现原始数值分析 ===")
    print("分析目标：车速波动、平均速度、最高速度、停车次数（保持原始单位）")
    print()

    # 数据目录
    data_dir = os.path.join('红绿灯停车分析', '车辆数据')

    if not os.path.exists(data_dir):
        print(f"错误：找不到目录 {data_dir}")
        return

    # 获取所有车辆文件
    vehicle_files = [f for f in os.listdir(data_dir) if f.endswith('.txt')]
    print(f"找到 {len(vehicle_files)} 个车辆数据文件")

    # 分析所有车辆的每日表现
    all_daily_data = []

    for vehicle_file in vehicle_files:
        vehicle_path = os.path.join(data_dir, vehicle_file)
        daily_analyses = analyze_daily_vehicle_performance(vehicle_path)
        all_daily_data.extend(daily_analyses)

    print(f"共分析了 {len(all_daily_data)} 个车辆-日期组合")

    # 计算车辆汇总统计
    vehicle_summaries = calculate_vehicle_summary_stats(all_daily_data)

    if len(vehicle_summaries) < 1:
        print("车辆数据不足")
        return

    print(f"有效车辆数量: {len(vehicle_summaries)}")

    # 保存结果
    df = pd.DataFrame(list(vehicle_summaries.values()))
    df.to_csv('车辆表现原始数值分析结果.csv', index=False, encoding='utf_8_sig')

    # 保存每日详细数据
    daily_df = pd.DataFrame(all_daily_data)
    daily_df.to_csv('车辆每日表现详细数据.csv', index=False, encoding='utf_8_sig')

    # 创建原始数值热力图和统计
    create_original_value_heatmaps(vehicle_summaries, all_daily_data)

    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- 车辆表现原始数值分析结果.csv")
    print("- 车辆每日表现详细数据.csv")
    print("- 车辆综合表现原始数值热力图.png")

if __name__ == '__main__':
    main()