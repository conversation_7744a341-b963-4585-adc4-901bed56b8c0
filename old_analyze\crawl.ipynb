{"cells": [{"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["import transbigdata as tbd\n", "import pandas as pd\n", "import geopandas as gpd\n", "from shapely.geometry import LineString, Point"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Obtaining city id: 重庆success\n", "Get bus data: 294路(曾家转盘-陈家桥新医院)\n", "Get bus data: 294路(陈家桥新医院-曾家转盘)\n", "294路(陈家桥新医院-曾家转盘) success\n", "294路(曾家转盘-陈家桥新医院) success\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\ProgramData\\anaconda3\\Lib\\site-packages\\transbigdata\\crawler.py:284: FutureWarning: You are adding a column named 'geometry' to a GeoDataFrame constructed without an active geometry column. Currently, this automatically sets the active geometry column to 'geometry' but in the future that will no longer happen. Instead, either provide geometry to the GeoDataFrame constructor (GeoDataFrame(... geometry=GeoSeries()) or use `set_geometry('geometry')` to explicitly set the active geometry column.\n", "  data['geometry'] = lines\n"]}], "source": ["# 读取数据\n", "BUS_GPS = pd.read_csv(r'D:\\\\workfile\\\\公交数据\\\\公交集团轨迹和刷卡数据\\\\每天的数据\\\\car_23039\\\\2024-11-02.csv', header=None)\n", "BUS_GPS.columns = [\"VehicleId\", \"GPSDateTime\", \"lon\", \"lat\", \"GpsSpeed\", \"Date\"]\n", "\n", "# 将 'lon' 和 'lat' 列转换为数值类型\n", "BUS_GPS['lon'] = pd.to_numeric(BUS_GPS['lon'], errors='coerce')\n", "BUS_GPS['lat'] = pd.to_numeric(BUS_GPS['lat'], errors='coerce')\n", "\n", "# 删除 'lon' 和 'lat' 列中包含 NaN 值的行\n", "BUS_GPS.dropna(subset=['lon', 'lat'], inplace=True)\n", "\n", "# 创建几何列\n", "BUS_GPS['geometry'] = BUS_GPS.apply(lambda row: Point(row['lon'], row['lat']), axis=1)\n", "\n", "# 获取公交数据\n", "line1, stop1 = tbd.getbusdata('重庆', ['294路(曾家转盘-陈家桥新医院)', '294路(陈家桥新医院-曾家转盘)'])\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["line = line1.iloc[:1].copy()  # 选择第一行 \n", "stop=stop1.iloc[:1].copy()  # 选择第一行"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning data...\n", "Position matching..."]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\ProgramData\\anaconda3\\Lib\\site-packages\\pyproj\\crs\\crs.py:143: FutureWarning: '+init=<authority>:<code>' syntax is deprecated. '<authority>:<code>' is the preferred initialization method. When making the change, be mindful of axis order changes: https://pyproj4.github.io/pyproj/stable/gotchas.html#axis-order-changes-in-proj-6\n", "  in_crs_string = _prepare_from_proj_string(in_crs_string)\n"]}, {"ename": "TypeError", "evalue": "unsupported operand type(s) for -: 'GeometryArray' and 'GeometryArray'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[36], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 计算到达信息\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m arriveinfo \u001b[38;5;241m=\u001b[39m tbd\u001b[38;5;241m.\u001b[39mbusgps_arriveinfo(BUS_GPS, line, stop)\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\Lib\\site-packages\\transbigdata\\busgps.py:142\u001b[0m, in \u001b[0;36mbusgps_arriveinfo\u001b[1;34m(data, line, stop, col, stopbuffer, mintime, disgap, project_epsg, timegap, projectoutput)\u001b[0m\n\u001b[0;32m    139\u001b[0m data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mproject_pre\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mproject\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mshift()\n\u001b[0;32m    140\u001b[0m \u001b[38;5;66;03m#定义三个条件\u001b[39;00m\n\u001b[0;32m    141\u001b[0m \u001b[38;5;66;03m# 1.车辆位置发生了变化\u001b[39;00m\n\u001b[1;32m--> 142\u001b[0m condition1 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mabs\u001b[39m(data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mproject_pre\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m-\u001b[39mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mproject\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;241m>\u001b[39mdisgap\n\u001b[0;32m    143\u001b[0m \u001b[38;5;66;03m# 2.相邻两条数据\"时间间隔大于30分钟\"\u001b[39;00m\n\u001b[0;32m    144\u001b[0m condition2 \u001b[38;5;241m=\u001b[39m (data[GPSDateTime]\u001b[38;5;241m-\u001b[39mdata[GPSDateTime\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_pre\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;241m.\u001b[39mdt\u001b[38;5;241m.\u001b[39mtotal_seconds()\u001b[38;5;241m>\u001b[39mtimegap\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\common.py:76\u001b[0m, in \u001b[0;36m_unpack_zerodim_and_defer.<locals>.new_method\u001b[1;34m(self, other)\u001b[0m\n\u001b[0;32m     72\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mNotImplemented\u001b[39m\n\u001b[0;32m     74\u001b[0m other \u001b[38;5;241m=\u001b[39m item_from_zerodim(other)\n\u001b[1;32m---> 76\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m method(\u001b[38;5;28mself\u001b[39m, other)\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\Lib\\site-packages\\pandas\\core\\arraylike.py:194\u001b[0m, in \u001b[0;36mOpsMixin.__sub__\u001b[1;34m(self, other)\u001b[0m\n\u001b[0;32m    192\u001b[0m \u001b[38;5;129m@unpack_zerodim_and_defer\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__sub__\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    193\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__sub__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[1;32m--> 194\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_arith_method(other, operator\u001b[38;5;241m.\u001b[39msub)\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\Lib\\site-packages\\pandas\\core\\series.py:6135\u001b[0m, in \u001b[0;36mSeries._arith_method\u001b[1;34m(self, other, op)\u001b[0m\n\u001b[0;32m   6133\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_arith_method\u001b[39m(\u001b[38;5;28mself\u001b[39m, other, op):\n\u001b[0;32m   6134\u001b[0m     \u001b[38;5;28mself\u001b[39m, other \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_align_for_op(other)\n\u001b[1;32m-> 6135\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m base\u001b[38;5;241m.\u001b[39mIndexOpsMixin\u001b[38;5;241m.\u001b[39m_arith_method(\u001b[38;5;28mself\u001b[39m, other, op)\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\Lib\\site-packages\\pandas\\core\\base.py:1382\u001b[0m, in \u001b[0;36mIndexOpsMixin._arith_method\u001b[1;34m(self, other, op)\u001b[0m\n\u001b[0;32m   1379\u001b[0m     rvalues \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marange(rvalues\u001b[38;5;241m.\u001b[39mstart, rvalues\u001b[38;5;241m.\u001b[39mstop, rvalues\u001b[38;5;241m.\u001b[39mstep)\n\u001b[0;32m   1381\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m np\u001b[38;5;241m.\u001b[39merrstate(\u001b[38;5;28mall\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m-> 1382\u001b[0m     result \u001b[38;5;241m=\u001b[39m ops\u001b[38;5;241m.\u001b[39marithmetic_op(lvalues, rvalues, op)\n\u001b[0;32m   1384\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_construct_result(result, name\u001b[38;5;241m=\u001b[39mres_name)\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py:273\u001b[0m, in \u001b[0;36marithmetic_op\u001b[1;34m(left, right, op)\u001b[0m\n\u001b[0;32m    260\u001b[0m \u001b[38;5;66;03m# NB: We assume that extract_array and ensure_wrapped_if_datetimelike\u001b[39;00m\n\u001b[0;32m    261\u001b[0m \u001b[38;5;66;03m#  have already been called on `left` and `right`,\u001b[39;00m\n\u001b[0;32m    262\u001b[0m \u001b[38;5;66;03m#  and `maybe_prepare_scalar_for_op` has already been called on `right`\u001b[39;00m\n\u001b[0;32m    263\u001b[0m \u001b[38;5;66;03m# We need to special-case datetime64/timedelta64 dtypes (e.g. because numpy\u001b[39;00m\n\u001b[0;32m    264\u001b[0m \u001b[38;5;66;03m# casts integer dtypes to timedelta64 when operating with timedelta64 - GH#22390)\u001b[39;00m\n\u001b[0;32m    266\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[0;32m    267\u001b[0m     should_extension_dispatch(left, right)\n\u001b[0;32m    268\u001b[0m     \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(right, (Timedelta, BaseOffset, Timestamp))\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    271\u001b[0m     \u001b[38;5;66;03m# Timedelta/Timestamp and other custom scalars are included in the check\u001b[39;00m\n\u001b[0;32m    272\u001b[0m     \u001b[38;5;66;03m# because numexpr will fail on it, see GH#31457\u001b[39;00m\n\u001b[1;32m--> 273\u001b[0m     res_values \u001b[38;5;241m=\u001b[39m op(left, right)\n\u001b[0;32m    274\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    275\u001b[0m     \u001b[38;5;66;03m# TODO we should handle EAs consistently and move this check before the if/else\u001b[39;00m\n\u001b[0;32m    276\u001b[0m     \u001b[38;5;66;03m# (https://github.com/pandas-dev/pandas/issues/41165)\u001b[39;00m\n\u001b[0;32m    277\u001b[0m     \u001b[38;5;66;03m# error: Argument 2 to \"_bool_arith_check\" has incompatible type\u001b[39;00m\n\u001b[0;32m    278\u001b[0m     \u001b[38;5;66;03m# \"Union[ExtensionArray, ndarray[Any, Any]]\"; expected \"ndarray[Any, Any]\"\u001b[39;00m\n\u001b[0;32m    279\u001b[0m     _bool_arith_check(op, left, right)  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n", "\u001b[1;31mTypeError\u001b[0m: unsupported operand type(s) for -: 'GeometryArray' and 'GeometryArray'"]}], "source": ["# 计算到达信息\n", "arriveinfo = tbd.busgps_arriveinfo(BUS_GPS, line, stop)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   stationnames           linename         lon        lat  \\\n", "0        陈家桥新医院  294路(陈家桥新医院-曾家转盘)  106.339491  29.617029   \n", "1          陈南路口  294路(陈家桥新医院-曾家转盘)  106.341308  29.611608   \n", "2     大学城北路东段路口  294路(陈家桥新医院-曾家转盘)  106.335350  29.610001   \n", "3        轨道陈家桥站  294路(陈家桥新医院-曾家转盘)  106.324727  29.610164   \n", "4        大学城东路东  294路(陈家桥新医院-曾家转盘)  106.316293  29.608722   \n", "5         大学城东路  294路(陈家桥新医院-曾家转盘)  106.315368  29.602679   \n", "6       大学城东路3站  294路(陈家桥新医院-曾家转盘)  106.314804  29.596149   \n", "7      重庆科技大学西门  294路(陈家桥新医院-曾家转盘)  106.314510  29.591357   \n", "8       大学城东路4站  294路(陈家桥新医院-曾家转盘)  106.314295  29.588785   \n", "9       大学城东路南段  294路(陈家桥新医院-曾家转盘)  106.313775  29.583780   \n", "10         康居西城  294路(陈家桥新医院-曾家转盘)  106.312862  29.580126   \n", "11       康居西城花市  294路(陈家桥新医院-曾家转盘)  106.309097  29.579152   \n", "12         康城南路  294路(陈家桥新医院-曾家转盘)  106.306724  29.575119   \n", "13          英业达  294路(陈家桥新医院-曾家转盘)  106.303621  29.565268   \n", "14         曾家转盘  294路(陈家桥新医院-曾家转盘)  106.299375  29.560237   \n", "0          曾家转盘  294路(曾家转盘-陈家桥新医院)  106.299375  29.560237   \n", "2           英业达  294路(曾家转盘-陈家桥新医院)  106.303813  29.565841   \n", "3          康城南路  294路(曾家转盘-陈家桥新医院)  106.306890  29.575142   \n", "4        康居西城花市  294路(曾家转盘-陈家桥新医院)  106.309195  29.579435   \n", "5          康居西城  294路(曾家转盘-陈家桥新医院)  106.312448  29.580039   \n", "6       大学城东路南段  294路(曾家转盘-陈家桥新医院)  106.314056  29.584061   \n", "7       大学城东路4站  294路(曾家转盘-陈家桥新医院)  106.314370  29.588717   \n", "8      重庆科技大学西门  294路(曾家转盘-陈家桥新医院)  106.314597  29.591453   \n", "9       大学城东路3站  294路(曾家转盘-陈家桥新医院)  106.314998  29.596327   \n", "10        大学城东路  294路(曾家转盘-陈家桥新医院)  106.315634  29.602871   \n", "11       大学城东路东  294路(曾家转盘-陈家桥新医院)  106.316412  29.608858   \n", "12       轨道陈家桥站  294路(曾家转盘-陈家桥新医院)  106.325296  29.609939   \n", "13    大学城北路东段路口  294路(曾家转盘-陈家桥新医院)  106.336660  29.609783   \n", "14         陈南路口  294路(曾家转盘-陈家桥新医院)  106.341490  29.612107   \n", "15       陈家桥新医院  294路(曾家转盘-陈家桥新医院)  106.339617  29.616841   \n", "\n", "                      geometry  line    id  \n", "0   POINT (106.33949 29.61703)  294路   1.0  \n", "1   POINT (106.34131 29.61161)  294路   2.0  \n", "2      POINT (106.33535 29.61)  294路   3.0  \n", "3   POINT (106.32473 29.61016)  294路   4.0  \n", "4   POINT (106.31629 29.60872)  294路   5.0  \n", "5   POINT (106.31537 29.60268)  294路   6.0  \n", "6    POINT (106.3148 29.59615)  294路   7.0  \n", "7   POINT (106.31451 29.59136)  294路   8.0  \n", "8    POINT (106.3143 29.58879)  294路   9.0  \n", "9   POINT (106.31377 29.58378)  294路  10.0  \n", "10  POINT (106.31286 29.58013)  294路  11.0  \n", "11   POINT (106.3091 29.57915)  294路  12.0  \n", "12  POINT (106.30672 29.57512)  294路  13.0  \n", "13  POINT (106.30362 29.56527)  294路  14.0  \n", "14  POINT (106.29937 29.56024)  294路  15.0  \n", "0   POINT (106.29937 29.56024)  294路   1.0  \n", "2   POINT (106.30381 29.56584)  294路   3.0  \n", "3   POINT (106.30689 29.57514)  294路   4.0  \n", "4   POINT (106.30919 29.57943)  294路   5.0  \n", "5   POINT (106.31245 29.58004)  294路   6.0  \n", "6   POINT (106.31406 29.58406)  294路   7.0  \n", "7   POINT (106.31437 29.58872)  294路   8.0  \n", "8    POINT (106.3146 29.59145)  294路   9.0  \n", "9     POINT (106.315 29.59633)  294路  10.0  \n", "10  POINT (106.31563 29.60287)  294路  11.0  \n", "11  POINT (106.31641 29.60886)  294路  12.0  \n", "12   POINT (106.3253 29.60994)  294路  13.0  \n", "13  POINT (106.33666 29.60978)  294路  14.0  \n", "14  POINT (106.34149 29.61211)  294路  15.0  \n", "15  POINT (106.33962 29.61684)  294路  16.0  \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["line1.plot()\n", "stop1.plot()\n", "print(stop1)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}