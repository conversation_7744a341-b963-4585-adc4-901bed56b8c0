import os
import csv
import math
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from datetime import datetime, time
from collections import defaultdict

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 地球半径（米）
EARTH_RADIUS = 6371000


def haversine(lon1, lat1, lon2, lat2):
    """计算两个经纬度坐标之间的距离（米）"""
    lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    return EARTH_RADIUS * c


def find_nearest_stop_line(lon, lat, stop_lines, threshold=40):
    """找到最近的停止线（阈值40米内），返回停止线名称和距离"""
    min_distance = float('inf')
    nearest_stop_line = None

    for _, stop_line in stop_lines.iterrows():
        distance = haversine(lon, lat, stop_line['经度'], stop_line['纬度'])
        if distance <= threshold and distance < min_distance:
            min_distance = distance
            nearest_stop_line = stop_line['停止线名称']

    return nearest_stop_line, min_distance if nearest_stop_line else None


def get_time_period(start_time):
    """根据起始时间判断时段 - 从6:00开始每隔1.5小时划分一个时段到21:00"""
    t = start_time.time()

    # 6:00-7:30
    if time(6, 0) <= t < time(7, 30):
        return "06:00-07:30"

    # 7:30-9:00
    elif time(7, 30) <= t < time(9, 0):
        return "07:30-09:00"

    # 9:00-10:30
    elif time(9, 0) <= t < time(10, 30):
        return "09:00-10:30"

    # 10:30-12:00
    elif time(10, 30) <= t < time(12, 0):
        return "10:30-12:00"

    # 12:00-13:30
    elif time(12, 0) <= t < time(13, 30):
        return "12:00-13:30"

    # 13:30-15:00
    elif time(13, 30) <= t < time(15, 0):
        return "13:30-15:00"

    # 15:00-16:30
    elif time(15, 0) <= t < time(16, 30):
        return "15:00-16:30"

    # 16:30-18:00
    elif time(16, 30) <= t < time(18, 0):
        return "16:30-18:00"

    # 18:00-19:30
    elif time(18, 0) <= t < time(19, 30):
        return "18:00-19:30"

    # 19:30-21:00
    elif time(19, 30) <= t < time(21, 0):
        return "19:30-21:00"

    # 其他时段忽略不计
    return None


def analyze_bus_data_by_time_period(trip_file, stop_lines):
    """按时段分析公交数据，每趟车经过路口时计算总停车时间"""
    # 读取轨迹数据
    data = []
    with open(trip_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过标题行
        for row in reader:
            if len(row) < 5:
                continue
            try:
                gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                lon = float(row[2])
                lat = float(row[3])
                speed = float(row[4])
                data.append((gps_time, lon, lat, speed))
            except ValueError:
                continue

    if not data:
        return None

    # 按时间排序
    data.sort(key=lambda x: x[0])

    # 按时段和路口分析停车事件
    time_period_stats = defaultdict(lambda: defaultdict(lambda: {
        'stop_events': 0,
        'total_stop_time': 0.0,
        'data_points': 0
    }))

    # 跟踪每个时段每个路口的经过状态
    # [time_period][intersection] = {'current_pass_stop_time': float, 'last_seen': time, 'has_pass_recorded': bool}
    intersection_states = defaultdict(lambda: defaultdict(lambda: {
        'current_pass_stop_time': 0.0,  # 当前这次经过的累计停车时间
        'last_seen': None,
        'has_pass_recorded': False,  # 这次经过是否已经记录过
        'last_speed': None
    }))

    for time_point, lon, lat, speed in data:
        # 判断当前时段
        time_period = get_time_period(time_point)
        if time_period is None:
            continue

        # 找到最近的停止线
        nearest_stop, _ = find_nearest_stop_line(lon, lat, stop_lines)
        if nearest_stop is None:
            continue

        # 记录数据点
        time_period_stats[time_period][nearest_stop]['data_points'] += 1

        state = intersection_states[time_period][nearest_stop]

        # 检查是否是新的一次经过（距离上次经过超过5分钟认为是新的一次经过）
        if state['last_seen'] is not None:
            time_diff = (time_point - state['last_seen']).total_seconds()
            if time_diff > 300:  # 5分钟
                # 结束上一次经过，记录停车事件
                stop_time = state['current_pass_stop_time']
                if stop_time is not None and stop_time >= 10 and not state['has_pass_recorded']:  # 至少停车10秒
                    time_period_stats[time_period][nearest_stop]['stop_events'] += 1
                    time_period_stats[time_period][nearest_stop]['total_stop_time'] += stop_time

                # 重置状态，开始新的一次经过
                state['current_pass_stop_time'] = 0.0
                state['has_pass_recorded'] = False

        # 计算这个时间点的停车时间（如果速度≤3km/h）
        if state['last_seen'] is not None:
            time_interval = (time_point - state['last_seen']).total_seconds()
            # 如果上一个点的速度≤3km/h，则累计停车时间
            if state['last_speed'] is not None and state['last_speed'] <= 3.0:
                state['current_pass_stop_time'] += time_interval

        state['last_seen'] = time_point
        state['last_speed'] = speed

    # 处理最后可能存在的经过
    for time_period in intersection_states:
        for intersection in intersection_states[time_period]:
            state = intersection_states[time_period][intersection]
            stop_time = state['current_pass_stop_time']
            if stop_time is not None and stop_time >= 10 and not state['has_pass_recorded']:  # 至少停车10秒
                time_period_stats[time_period][intersection]['stop_events'] += 1
                time_period_stats[time_period][intersection]['total_stop_time'] += stop_time

    return {
        'file_name': os.path.basename(trip_file),
        'car_num': os.path.basename(trip_file).replace('CarNum_', '').replace('.txt', ''),
        'time_period_stats': dict(time_period_stats)
    }


def main():
    """主处理流程"""
    # 读取停止线数据（上行）
    stop_lines = pd.read_excel('红绿灯停车分析/停止线点坐标（上行）.xlsx')
    stop_lines = stop_lines[stop_lines['方向'] == '上行']
    
    print(f"加载了 {len(stop_lines)} 个上行停止线坐标")
    print("停止线列表:")
    for _, stop_line in stop_lines.iterrows():
        print(f"  - {stop_line['停止线名称']}: ({stop_line['经度']}, {stop_line['纬度']})")

    # 获取所有上行数据文件
    upward_data_dir = '上行数据'
    trip_files = [f for f in os.listdir(upward_data_dir) if f.endswith('.txt')]
    
    print(f"\n找到 {len(trip_files)} 个上行数据文件")

    # 存储所有分析结果
    all_results = []
    intersection_summary = defaultdict(lambda: defaultdict(lambda: {
        'total_trips': 0,
        'total_stop_events': 0,
        'total_stop_time': 0,
        'trips_with_stops': 0
    }))

    # 分析每个轨迹文件
    for i, trip_file in enumerate(trip_files, 1):
        print(f"正在分析文件 {i}/{len(trip_files)}: {trip_file}")

        result = analyze_bus_data_by_time_period(
            os.path.join(upward_data_dir, trip_file),
            stop_lines
        )

        if result:
            all_results.append(result)

            # 更新路口汇总统计
            for time_period, intersections in result['time_period_stats'].items():
                for intersection, stats in intersections.items():
                    if stats['data_points'] > 0:  # 只统计有数据的路口
                        intersection_summary[intersection][time_period]['total_trips'] += 1
                        intersection_summary[intersection][time_period]['total_stop_events'] += stats['stop_events']
                        intersection_summary[intersection][time_period]['total_stop_time'] += stats['total_stop_time']
                        if stats['stop_events'] > 0:
                            intersection_summary[intersection][time_period]['trips_with_stops'] += 1

    print(f"\n分析完成! 共分析了 {len(all_results)} 趟公交车数据")

    # 生成详细结果CSV
    detailed_results = []
    for result in all_results:
        base_info = {
            'file_name': result['file_name'],
            'car_num': result['car_num']
        }

        if result['time_period_stats']:
            for time_period, intersections in result['time_period_stats'].items():
                for intersection, stats in intersections.items():
                    if stats['data_points'] > 0:  # 只记录有数据的
                        row = base_info.copy()
                        row.update({
                            'time_period': time_period,
                            'intersection': intersection,
                            'stop_events': stats['stop_events'],
                            'total_stop_time': stats['total_stop_time'],
                            'data_points': stats['data_points']
                        })
                        detailed_results.append(row)

    # 保存详细结果
    df_detailed = pd.DataFrame(detailed_results)
    df_detailed.to_csv('上行数据红绿灯详细分析.csv', index=False, encoding='utf_8_sig')
    print(f"详细结果已保存到: 上行数据红绿灯详细分析.csv")

    # 生成路口汇总统计CSV
    summary_results = []
    for intersection, time_periods in intersection_summary.items():
        for time_period, stats in time_periods.items():
            avg_stop_events = stats['total_stop_events'] / stats['total_trips'] if stats['total_trips'] > 0 else 0
            avg_stop_time = stats['total_stop_time'] / stats['total_trips'] if stats['total_trips'] > 0 else 0
            stop_probability = stats['trips_with_stops'] / stats['total_trips'] if stats['total_trips'] > 0 else 0
            
            summary_results.append({
                'intersection': intersection,
                'time_period': time_period,
                'total_trips': stats['total_trips'],
                'trips_with_stops': stats['trips_with_stops'],
                'stop_probability': stop_probability * 100,  # 转换为百分比
                'total_stop_events': stats['total_stop_events'],
                'avg_stop_events_per_trip': avg_stop_events,
                'total_stop_time': stats['total_stop_time'],
                'avg_stop_time_per_trip': avg_stop_time
            })

    df_summary = pd.DataFrame(summary_results)
    df_summary = df_summary.sort_values(['intersection', 'time_period'])
    df_summary.to_csv('上行数据路口红绿灯汇总统计.csv', index=False, encoding='utf_8_sig')
    print(f"路口汇总统计已保存到: 上行数据路口红绿灯汇总统计.csv")

    # 打印汇总统计
    print("\n=== 路口红绿灯汇总统计（新时段划分）===")
    time_periods_order = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00",
                         "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00",
                         "18:00-19:30", "19:30-21:00"]

    for intersection in sorted(intersection_summary.keys()):
        print(f"\n【{intersection}】")
        for time_period in time_periods_order:
            if time_period in intersection_summary[intersection]:
                stats = intersection_summary[intersection][time_period]
                avg_stop_events = stats['total_stop_events'] / stats['total_trips'] if stats['total_trips'] > 0 else 0
                avg_stop_time = stats['total_stop_time'] / stats['total_trips'] if stats['total_trips'] > 0 else 0
                stop_probability = stats['trips_with_stops'] / stats['total_trips'] if stats['total_trips'] > 0 else 0

                print(f"  {time_period}:")
                print(f"    总行程数: {stats['total_trips']}")
                print(f"    有停车行程数: {stats['trips_with_stops']}")
                print(f"    停车概率: {stop_probability:.1%}")
                print(f"    平均停车次数: {avg_stop_events:.2f}")
                print(f"    平均停车时间: {avg_stop_time:.1f}秒")


def create_visualizations():
    """创建可视化图表"""
    # 读取汇总统计数据
    df_summary = pd.read_csv('上行数据路口红绿灯汇总统计.csv', encoding='utf_8_sig')

    # 1. 各路口停车概率对比图
    plt.figure(figsize=(15, 10))

    # 按时段分组的停车概率
    time_periods = ["06:00-07:30", "07:30-09:00", "09:00-10:30", "10:30-12:00",
                   "12:00-13:30", "13:30-15:00", "15:00-16:30", "16:30-18:00",
                   "18:00-19:30", "19:30-21:00"]
    colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFCE56", "#FF9F40",
             "#C9CBCF", "#4BC0C0", "#9966FF", "#FF6384"]

    intersections = df_summary['intersection'].unique().tolist()
    x = np.arange(len(intersections))
    width = 0.2

    for i, period in enumerate(time_periods):
        period_data = df_summary[df_summary['time_period'] == period]
        stop_probs = []
        for intersection in intersections:
            intersection_data = period_data[period_data['intersection'] == intersection]
            if not intersection_data.empty:
                stop_probs.append(intersection_data['stop_probability'].iloc[0])
            else:
                stop_probs.append(0)

        plt.bar(x + i * width, stop_probs, width, label=period, color=colors[i])

    plt.xlabel('交叉口', fontsize=12)
    plt.ylabel('停车概率 (%)', fontsize=12)
    plt.title('各路口分时段停车概率对比', fontsize=14, fontweight='bold')
    plt.xticks(x + width * 1.5, intersections, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('上行数据_各路口停车概率对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 各路口平均停车时间对比图
    plt.figure(figsize=(15, 10))

    for i, period in enumerate(time_periods):
        period_data = df_summary[df_summary['time_period'] == period]
        avg_stop_times = []
        for intersection in intersections:
            intersection_data = period_data[period_data['intersection'] == intersection]
            if not intersection_data.empty:
                avg_stop_times.append(intersection_data['avg_stop_time_per_trip'].iloc[0])
            else:
                avg_stop_times.append(0)

        plt.bar(x + i * width, avg_stop_times, width, label=period, color=colors[i])

    plt.xlabel('交叉口', fontsize=12)
    plt.ylabel('平均停车时间 (秒)', fontsize=12)
    plt.title('各路口分时段平均停车时间对比', fontsize=14, fontweight='bold')
    plt.xticks(x + width * 1.5, intersections, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('上行数据_各路口平均停车时间对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 各路口平均停车次数对比图
    plt.figure(figsize=(15, 10))

    for i, period in enumerate(time_periods):
        period_data = df_summary[df_summary['time_period'] == period]
        avg_stop_events = []
        for intersection in intersections:
            intersection_data = period_data[period_data['intersection'] == intersection]
            if not intersection_data.empty:
                avg_stop_events.append(intersection_data['avg_stop_events_per_trip'].iloc[0])
            else:
                avg_stop_events.append(0)

        plt.bar(x + i * width, avg_stop_events, width, label=period, color=colors[i])

    plt.xlabel('交叉口', fontsize=12)
    plt.ylabel('平均停车次数', fontsize=12)
    plt.title('各路口分时段平均停车次数对比', fontsize=14, fontweight='bold')
    plt.xticks(x + width * 1.5, intersections, rotation=45, ha='right')
    plt.legend()
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig('上行数据_各路口平均停车次数对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 4. 热力图：各路口各时段停车时间
    pivot_data = df_summary.pivot(index='intersection', columns='time_period', values='avg_stop_time_per_trip')
    pivot_data = pivot_data.reindex(columns=time_periods)
    pivot_data = pivot_data.fillna(0)

    plt.figure(figsize=(12, 10))
    im = plt.imshow(pivot_data.values, cmap='YlOrRd', aspect='auto')
    plt.colorbar(im, label='平均停车时间 (秒)')
    plt.xticks(range(len(time_periods)), time_periods)
    plt.yticks(range(len(intersections)), intersections)
    plt.xlabel('时段', fontsize=12)
    plt.ylabel('交叉口', fontsize=12)
    plt.title('各路口各时段平均停车时间热力图', fontsize=14, fontweight='bold')

    # 在每个格子中添加数值
    for i in range(len(intersections)):
        for j in range(len(time_periods)):
            value = pivot_data.iloc[i, j]
            try:
                if pd.notna(value) and value > 0:
                    plt.text(j, i, f'{value:.0f}', ha='center', va='center',
                            color='white' if value > pivot_data.values.max()/2 else 'black')
            except:
                continue

    plt.tight_layout()
    plt.savefig('上行数据_停车时间热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("可视化图表已生成完成！")


if __name__ == '__main__':
    main()
    print("\n正在生成可视化图表...")
    create_visualizations()
