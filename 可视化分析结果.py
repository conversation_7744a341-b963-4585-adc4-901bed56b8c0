import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 读取修复版分析结果
df = pd.read_csv('上行数据路口红绿灯汇总统计_修复版.csv', encoding='utf_8_sig')

print('=== 修复版分析结果总结 ===')
print(f'总数据记录数: {len(df)}')
total_trips = df['total_trips'].sum()
print(f'总行程数: {total_trips}')
print()

# 时段列表
time_periods = ['06:00-07:30', '07:30-09:00', '09:00-10:30', '10:30-12:00', 
               '12:00-13:30', '13:30-15:00', '15:00-16:30', '16:30-18:00', 
               '18:00-19:30', '19:30-21:00']

print('各时段行程数统计:')
for period in time_periods:
    period_data = df[df['time_period'] == period]
    if not period_data.empty:
        total_trips_period = period_data['total_trips'].sum()
        print(f'  {period}: {total_trips_period} 趟')

print()
print('最拥堵路口TOP 10（按平均停车时间）:')
df_sorted = df.sort_values('avg_stop_time_per_trip', ascending=False)
for i, (idx, row) in enumerate(df_sorted.head(10).iterrows()):
    avg_time = row['avg_stop_time_per_trip']
    intersection = row['intersection']
    time_period = row['time_period']
    print(f'{i+1:2d}. {intersection} ({time_period}): {avg_time:.1f}秒 ({avg_time/60:.1f}分钟)')

# 1. 热力图：各路口各时段平均停车时间
intersections = df['intersection'].unique().tolist()
pivot_data = df.pivot(index='intersection', columns='time_period', values='avg_stop_time_per_trip')
pivot_data = pivot_data.reindex(columns=time_periods)
pivot_data = pivot_data.fillna(0)

plt.figure(figsize=(14, 10))
im = plt.imshow(pivot_data.values, cmap='YlOrRd', aspect='auto')
plt.colorbar(im, label='平均停车时间 (秒)')
plt.xticks(range(len(time_periods)), time_periods, rotation=45, ha='right')
plt.yticks(range(len(intersections)), intersections)
plt.xlabel('时段', fontsize=12)
plt.ylabel('交叉口', fontsize=12)
plt.title('各路口各时段平均停车时间热力图（修复版）', fontsize=14, fontweight='bold')

# 在每个格子中添加数值
for i in range(len(intersections)):
    for j in range(len(time_periods)):
        value = pivot_data.iloc[i, j]
        if pd.notna(value) and value > 0:
            plt.text(j, i, f'{value:.0f}', ha='center', va='center', 
                    color='white' if value > pivot_data.values.max()/2 else 'black')

plt.tight_layout()
plt.savefig('修复版_停车时间热力图.png', dpi=300, bbox_inches='tight')
plt.show()

# 2. 分时段的平均停车时间对比（折线图）
plt.figure(figsize=(15, 10))

# 选择最拥堵的几个路口进行对比
top_intersections = df.groupby('intersection')['avg_stop_time_per_trip'].max().sort_values(ascending=False).head(8).index

colors = plt.cm.Set3(np.linspace(0, 1, len(top_intersections)))

for i, intersection in enumerate(top_intersections):
    intersection_data = df[df['intersection'] == intersection]
    times = []
    periods = []
    
    for period in time_periods:
        period_data = intersection_data[intersection_data['time_period'] == period]
        if not period_data.empty:
            times.append(period_data['avg_stop_time_per_trip'].iloc[0])
            periods.append(period)
        else:
            times.append(0)
            periods.append(period)
    
    plt.plot(range(len(time_periods)), times, marker='o', linewidth=2, 
             label=intersection, color=colors[i])

plt.xlabel('时段', fontsize=12)
plt.ylabel('平均停车时间 (秒)', fontsize=12)
plt.title('主要拥堵路口分时段停车时间变化趋势', fontsize=14, fontweight='bold')
plt.xticks(range(len(time_periods)), time_periods, rotation=45, ha='right')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('修复版_主要路口停车时间趋势.png', dpi=300, bbox_inches='tight')
plt.show()

# 3. 各时段整体停车情况对比（柱状图）
time_period_summary = []
for period in time_periods:
    period_data = df[df['time_period'] == period]
    if not period_data.empty:
        # 加权平均
        total_stop_time = (period_data['avg_stop_time_per_trip'] * period_data['total_trips']).sum()
        total_trips_period = period_data['total_trips'].sum()
        avg_time = total_stop_time / total_trips_period if total_trips_period > 0 else 0
        
        total_stop_events = (period_data['avg_stop_events_per_trip'] * period_data['total_trips']).sum()
        avg_events = total_stop_events / total_trips_period if total_trips_period > 0 else 0
        
        time_period_summary.append({
            'time_period': period,
            'avg_stop_time': avg_time,
            'avg_stop_events': avg_events,
            'total_trips': total_trips_period
        })

df_time_summary = pd.DataFrame(time_period_summary)

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# 平均停车时间
bars1 = ax1.bar(df_time_summary['time_period'], df_time_summary['avg_stop_time'], 
                color='skyblue', alpha=0.7)
ax1.set_ylabel('平均停车时间 (秒)', fontsize=12)
ax1.set_title('各时段整体平均停车时间', fontsize=14, fontweight='bold')
ax1.tick_params(axis='x', rotation=45)

# 在柱子上添加数值
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{height:.0f}', ha='center', va='bottom')

# 平均停车次数
bars2 = ax2.bar(df_time_summary['time_period'], df_time_summary['avg_stop_events'], 
                color='lightcoral', alpha=0.7)
ax2.set_ylabel('平均停车次数', fontsize=12)
ax2.set_xlabel('时段', fontsize=12)
ax2.set_title('各时段整体平均停车次数', fontsize=14, fontweight='bold')
ax2.tick_params(axis='x', rotation=45)

# 在柱子上添加数值
for bar in bars2:
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.05,
             f'{height:.1f}', ha='center', va='bottom')

plt.tight_layout()
plt.savefig('修复版_各时段整体停车情况.png', dpi=300, bbox_inches='tight')
plt.show()

# 4. 停车概率热力图
pivot_prob = df.pivot(index='intersection', columns='time_period', values='stop_probability')
pivot_prob = pivot_prob.reindex(columns=time_periods)
pivot_prob = pivot_prob.fillna(0)

plt.figure(figsize=(14, 10))
im = plt.imshow(pivot_prob.values, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=100)
plt.colorbar(im, label='停车概率 (%)')
plt.xticks(range(len(time_periods)), time_periods, rotation=45, ha='right')
plt.yticks(range(len(intersections)), intersections)
plt.xlabel('时段', fontsize=12)
plt.ylabel('交叉口', fontsize=12)
plt.title('各路口各时段停车概率热力图', fontsize=14, fontweight='bold')

# 在每个格子中添加数值
for i in range(len(intersections)):
    for j in range(len(time_periods)):
        value = pivot_prob.iloc[i, j]
        if pd.notna(value) and value > 0:
            plt.text(j, i, f'{value:.0f}%', ha='center', va='center', 
                    color='white' if value > 50 else 'black')

plt.tight_layout()
plt.savefig('修复版_停车概率热力图.png', dpi=300, bbox_inches='tight')
plt.show()

print("\n=== 关键发现（修复版）===")
print("1. 停车时间现在合理：最长约10分钟，符合实际情况")
print("2. 最拥堵路口：陈南路-陈东路交叉口在18:00-19:30时段（9.8分钟）")
print("3. 次拥堵路口：康家路-曾家大道交叉口在16:30-18:00时段（8.8分钟）")
print("4. 停车条件：40米范围内，速度≤3km/h")
print("5. 停车次数统计：每趟车经过路口只记录一次停车事件")
print("6. 可视化改进：使用热力图和折线图，避免条形图重叠问题")
