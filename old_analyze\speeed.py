import pandas as pd
import matplotlib.pyplot as plt

# 读取CSV文件
df = pd.read_csv(r'd:\\workfile\\公交数据\\公交集团轨迹和刷卡数据\\尝试性方法\\2024-11-04.csv')

# 过滤掉无效坐标点（经纬度为0）
df = df[(df['Longitude'] != 0) & (df['Latitude'] != 0)]

# 计算速度最大值，用于定义分箱范围
max_speed = df['GpsSpeed'].max()

# 定义分箱边界：0为第一类，之后每5m/s为一个区间
bins = [0] + list(range(5, int(max_speed) + 5, 5))

# 对速度进行分箱
df['speed_bin'] = pd.cut(df['GpsSpeed'], bins=bins, include_lowest=True)

# 绘图
plt.figure(figsize=(10, 8))
scatter = plt.scatter(
    df['Longitude'],
    df['Latitude'],
    c=df['speed_bin'].cat.codes,
    cmap='viridis',
    s=10,
    alpha=0.7
)

# 添加颜色条
cbar = plt.colorbar(scatter, label='GpsSpeed (m/s)')
cbar.set_ticks(range(len(bins)))
cbar.set_ticklabels([f'{b1}-{b2}' if i > 0 else '0' for i, (b1, b2) in enumerate(zip(bins, bins[1:]))] + [f'>{bins[-1]}'])

# 添加标签和标题
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.title('Bus Speed Distribution by GPS Coordinates')
plt.grid(True)

# 显示图像
plt.tight_layout()
plt.show()