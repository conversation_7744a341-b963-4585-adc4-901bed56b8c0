{"cells": [{"cell_type": "code", "execution_count": 3, "id": "bf81be74", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["从Excel文件读取了 16 个坐标点\n", "前5个坐标点:\n", "  1. 陈东路-陈丰路: (29.613937, 106.341562)\n", "  2. 大学城北路-西永九号: (29.6099974911748, 106.336314934283)\n", "  3. 大学城北路大学城东一路: (29.6101232835445, 106.327124053259)\n", "  4. 大学城北路-大学城东路: (29.6100515458433, 106.316780144172)\n", "  5. 大学城东路-学城大道: (29.606043402962, 106.315811453004)\n"]}, {"data": {"text/html": ["<div style=\"width:100%;\"><div style=\"position:relative;width:100%;height:0;padding-bottom:60%;\"><span style=\"color:#565656\">Make this Notebook Trusted to load map: File -> Trust Notebook</span><iframe srcdoc=\"&lt;!DOCTYPE html&gt;\n", "&lt;html&gt;\n", "&lt;head&gt;\n", "    \n", "    &lt;meta http-equiv=&quot;content-type&quot; content=&quot;text/html; charset=UTF-8&quot; /&gt;\n", "    &lt;script src=&quot;https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://code.jquery.com/jquery-3.7.1.min.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js&quot;&gt;&lt;/script&gt;\n", "    &lt;script src=&quot;https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js&quot;&gt;&lt;/script&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css&quot;/&gt;\n", "    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css&quot;/&gt;\n", "    \n", "            &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width,\n", "                initial-scale=1.0, maximum-scale=1.0, user-scalable=no&quot; /&gt;\n", "            &lt;style&gt;\n", "                #map_ccf7f4c37f7ae78047bd2db7d26c8df0 {\n", "                    position: relative;\n", "                    width: 100.0%;\n", "                    height: 100.0%;\n", "                    left: 0.0%;\n", "                    top: 0.0%;\n", "                }\n", "                .leaflet-container { font-size: 1rem; }\n", "            &lt;/style&gt;\n", "\n", "            &lt;style&gt;html, body {\n", "                width: 100%;\n", "                height: 100%;\n", "                margin: 0;\n", "                padding: 0;\n", "            }\n", "            &lt;/style&gt;\n", "\n", "            &lt;style&gt;#map {\n", "                position:absolute;\n", "                top:0;\n", "                bottom:0;\n", "                right:0;\n", "                left:0;\n", "                }\n", "            &lt;/style&gt;\n", "\n", "            &lt;script&gt;\n", "                L_NO_TOUCH = false;\n", "                L_DISABLE_3D = false;\n", "            &lt;/script&gt;\n", "\n", "        \n", "&lt;/head&gt;\n", "&lt;body&gt;\n", "    \n", "    \n", "            &lt;div class=&quot;folium-map&quot; id=&quot;map_ccf7f4c37f7ae78047bd2db7d26c8df0&quot; &gt;&lt;/div&gt;\n", "        \n", "&lt;/body&gt;\n", "&lt;script&gt;\n", "    \n", "    \n", "            var map_ccf7f4c37f7ae78047bd2db7d26c8df0 = L.map(\n", "                &quot;map_ccf7f4c37f7ae78047bd2db7d26c8df0&quot;,\n", "                {\n", "                    center: [29.590738353610092, 106.3150097158453],\n", "                    crs: L.CRS.EPSG3857,\n", "                    ...{\n", "  &quot;zoom&quot;: 13,\n", "  &quot;zoomControl&quot;: true,\n", "  &quot;preferCanvas&quot;: false,\n", "}\n", "\n", "                }\n", "            );\n", "\n", "            \n", "\n", "        \n", "    \n", "            var tile_layer_19a72d11b1e40950b3e01a3cbf0d8504 = L.tileLayer(\n", "                &quot;https://tile.openstreetmap.org/{z}/{x}/{y}.png&quot;,\n", "                {\n", "  &quot;minZoom&quot;: 0,\n", "  &quot;max<PERSON>oom&quot;: 19,\n", "  &quot;maxNative<PERSON>oom&quot;: 19,\n", "  &quot;noWrap&quot;: false,\n", "  &quot;attribution&quot;: &quot;\\u0026copy; \\u003ca href=\\&quot;https://www.openstreetmap.org/copyright\\&quot;\\u003eOpenStreetMap\\u003c/a\\u003e contributors&quot;,\n", "  &quot;subdomains&quot;: &quot;abc&quot;,\n", "  &quot;detectRetina&quot;: false,\n", "  &quot;tms&quot;: false,\n", "  &quot;opacity&quot;: 1,\n", "}\n", "\n", "            );\n", "        \n", "    \n", "            tile_layer_19a72d11b1e40950b3e01a3cbf0d8504.addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "            var marker_4eabe5f020b998222f9349f19070fb26 = L.marker(\n", "                [29.613937, 106.341562],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_21cd3bcd570905b61e6664b859920932 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_e5e19ba5a2df914a1dafa41fa2d20bdf = $(`&lt;div id=&quot;html_e5e19ba5a2df914a1dafa41fa2d20bdf&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;陈东路-陈丰路&lt;/div&gt;`)[0];\n", "                popup_21cd3bcd570905b61e6664b859920932.setContent(html_e5e19ba5a2df914a1dafa41fa2d20bdf);\n", "            \n", "        \n", "\n", "        marker_4eabe5f020b998222f9349f19070fb26.bindPopup(popup_21cd3bcd570905b61e6664b859920932)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_4eabe5f020b998222f9349f19070fb26.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点1: 陈东路-陈丰路\n", "坐标: (29.613937, 106.341562)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_6afe2f280d53871b235f3ffd4afd57ff = L.marker(\n", "                [29.6099974911748, 106.336314934283],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_0bc2424919f955ba6934df2f97498a87 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_27341af05ab6c247d705f0d7bebecafc = $(`&lt;div id=&quot;html_27341af05ab6c247d705f0d7bebecafc&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城北路-西永九号&lt;/div&gt;`)[0];\n", "                popup_0bc2424919f955ba6934df2f97498a87.setContent(html_27341af05ab6c247d705f0d7bebecafc);\n", "            \n", "        \n", "\n", "        marker_6afe2f280d53871b235f3ffd4afd57ff.bindPopup(popup_0bc2424919f955ba6934df2f97498a87)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_6afe2f280d53871b235f3ffd4afd57ff.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点2: 大学城北路-西永九号\n", "坐标: (29.6099974911748, 106.336314934283)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_fc002e175a98e98e8011e4a24b3a15eb = L.marker(\n", "                [29.6101232835445, 106.327124053259],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_b48bbe8a4cb383f65af7f8bf8b65012d = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_6330a1ac1336f5ca213fe5a8e1b5687a = $(`&lt;div id=&quot;html_6330a1ac1336f5ca213fe5a8e1b5687a&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城北路大学城东一路&lt;/div&gt;`)[0];\n", "                popup_b48bbe8a4cb383f65af7f8bf8b65012d.setContent(html_6330a1ac1336f5ca213fe5a8e1b5687a);\n", "            \n", "        \n", "\n", "        marker_fc002e175a98e98e8011e4a24b3a15eb.bindPopup(popup_b48bbe8a4cb383f65af7f8bf8b65012d)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_fc002e175a98e98e8011e4a24b3a15eb.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点3: 大学城北路大学城东一路\n", "坐标: (29.6101232835445, 106.327124053259)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_4dd6ad83a016bd12af27eb0648f82bc7 = L.marker(\n", "                [29.6100515458433, 106.316780144172],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_0090913cb4b6fb9c1a5687d3f95aa915 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_576adac2d7b1ecb5359ca8c2997a8ebc = $(`&lt;div id=&quot;html_576adac2d7b1ecb5359ca8c2997a8ebc&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城北路-大学城东路&lt;/div&gt;`)[0];\n", "                popup_0090913cb4b6fb9c1a5687d3f95aa915.setContent(html_576adac2d7b1ecb5359ca8c2997a8ebc);\n", "            \n", "        \n", "\n", "        marker_4dd6ad83a016bd12af27eb0648f82bc7.bindPopup(popup_0090913cb4b6fb9c1a5687d3f95aa915)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_4dd6ad83a016bd12af27eb0648f82bc7.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点4: 大学城北路-大学城东路\n", "坐标: (29.6100515458433, 106.316780144172)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_3bb9ab1656adb661bd22033d8b7b179e = L.marker(\n", "                [29.606043402962, 106.315811453004],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_fd8d9f0aebec856ad89c4631ac5eb0a9 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_a1cc1fe61865feebe06a3f2a2e855de4 = $(`&lt;div id=&quot;html_a1cc1fe61865feebe06a3f2a2e855de4&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-学城大道&lt;/div&gt;`)[0];\n", "                popup_fd8d9f0aebec856ad89c4631ac5eb0a9.setContent(html_a1cc1fe61865feebe06a3f2a2e855de4);\n", "            \n", "        \n", "\n", "        marker_3bb9ab1656adb661bd22033d8b7b179e.bindPopup(popup_fd8d9f0aebec856ad89c4631ac5eb0a9)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_3bb9ab1656adb661bd22033d8b7b179e.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点5: 大学城东路-学城大道\n", "坐标: (29.606043402962, 106.315811453004)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_3352abca75407a5a758e3e366da3c0d5 = L.marker(\n", "                [29.6014166617525, 106.315256562647],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_49af18383a2a226ac42e1af2d23ac179 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_aa9d3fe2e78688433b704e21e47b41b7 = $(`&lt;div id=&quot;html_aa9d3fe2e78688433b704e21e47b41b7&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-大学城南路&lt;/div&gt;`)[0];\n", "                popup_49af18383a2a226ac42e1af2d23ac179.setContent(html_aa9d3fe2e78688433b704e21e47b41b7);\n", "            \n", "        \n", "\n", "        marker_3352abca75407a5a758e3e366da3c0d5.bindPopup(popup_49af18383a2a226ac42e1af2d23ac179)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_3352abca75407a5a758e3e366da3c0d5.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点6: 大学城东路-大学城南路\n", "坐标: (29.6014166617525, 106.315256562647)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_ba70cca9eba5c88cb893d18ca4905eac = L.marker(\n", "                [29.5976571288982, 106.314972560237],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_b65fc8594642ae61f2b563c1fbd78df1 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_c4f1dd1152a0ef1b6bbae86ef6005193 = $(`&lt;div id=&quot;html_c4f1dd1152a0ef1b6bbae86ef6005193&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-景阳路&lt;/div&gt;`)[0];\n", "                popup_b65fc8594642ae61f2b563c1fbd78df1.setContent(html_c4f1dd1152a0ef1b6bbae86ef6005193);\n", "            \n", "        \n", "\n", "        marker_ba70cca9eba5c88cb893d18ca4905eac.bindPopup(popup_b65fc8594642ae61f2b563c1fbd78df1)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_ba70cca9eba5c88cb893d18ca4905eac.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点7: 大学城东路-景阳路\n", "坐标: (29.5976571288982, 106.314972560237)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_751a4b8376351b08b964f64b6640156d = L.marker(\n", "                [29.595189245757, 106.314791877599],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_a2dc9c7ba974d3b370c866cf237e0d05 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_eb2fd48677eca7112ebae6e1b555aaf4 = $(`&lt;div id=&quot;html_eb2fd48677eca7112ebae6e1b555aaf4&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-汇贤路&lt;/div&gt;`)[0];\n", "                popup_a2dc9c7ba974d3b370c866cf237e0d05.setContent(html_eb2fd48677eca7112ebae6e1b555aaf4);\n", "            \n", "        \n", "\n", "        marker_751a4b8376351b08b964f64b6640156d.bindPopup(popup_a2dc9c7ba974d3b370c866cf237e0d05)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_751a4b8376351b08b964f64b6640156d.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点8: 大学城东路-汇贤路\n", "坐标: (29.595189245757, 106.314791877599)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_3581b00f154ececb98ca1d5c397c9d47 = L.marker(\n", "                [29.5903591037896, 106.314347785757],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_1c503c2e0e363c8f7a078eeca98b205b = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_07ddbcdbecacc09686241e8e14cf0af1 = $(`&lt;div id=&quot;html_07ddbcdbecacc09686241e8e14cf0af1&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-思贤路&lt;/div&gt;`)[0];\n", "                popup_1c503c2e0e363c8f7a078eeca98b205b.setContent(html_07ddbcdbecacc09686241e8e14cf0af1);\n", "            \n", "        \n", "\n", "        marker_3581b00f154ececb98ca1d5c397c9d47.bindPopup(popup_1c503c2e0e363c8f7a078eeca98b205b)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_3581b00f154ececb98ca1d5c397c9d47.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点9: 大学城东路-思贤路\n", "坐标: (29.5903591037896, 106.314347785757)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_e17b06d715a28d55e0961a771ea0420c = L.marker(\n", "                [29.5868852593263, 106.314089825678],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_626c15c4fb9a9fa79d01eaeea23d7563 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_9a250750698da6cdfb8d97beac797476 = $(`&lt;div id=&quot;html_9a250750698da6cdfb8d97beac797476&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-大学城南一路&lt;/div&gt;`)[0];\n", "                popup_626c15c4fb9a9fa79d01eaeea23d7563.setContent(html_9a250750698da6cdfb8d97beac797476);\n", "            \n", "        \n", "\n", "        marker_e17b06d715a28d55e0961a771ea0420c.bindPopup(popup_626c15c4fb9a9fa79d01eaeea23d7563)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_e17b06d715a28d55e0961a771ea0420c.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点10: 大学城东路-大学城南一路\n", "坐标: (29.5868852593263, 106.314089825678)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_f78d38a9e62597d20fd15477d7fcb993 = L.marker(\n", "                [29.5827280212354, 106.313715455549],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_b6610b71ef21ab2293dfa66f1c29aa1c = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_1aaa53fbb21f301e682c334c7530b62f = $(`&lt;div id=&quot;html_1aaa53fbb21f301e682c334c7530b62f&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;大学城东路-大学城南二路&lt;/div&gt;`)[0];\n", "                popup_b6610b71ef21ab2293dfa66f1c29aa1c.setContent(html_1aaa53fbb21f301e682c334c7530b62f);\n", "            \n", "        \n", "\n", "        marker_f78d38a9e62597d20fd15477d7fcb993.bindPopup(popup_b6610b71ef21ab2293dfa66f1c29aa1c)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_f78d38a9e62597d20fd15477d7fcb993.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点11: 大学城东路-大学城南二路\n", "坐标: (29.5827280212354, 106.313715455549)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_ece8f982f892ba598b17dda0ee1352cb = L.marker(\n", "                [29.5802331987334, 106.309370587699],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_56846bd0da2ccf74dde1928095e53151 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_9e5dc304a3f3f07fd8f324be6ac6c1a0 = $(`&lt;div id=&quot;html_9e5dc304a3f3f07fd8f324be6ac6c1a0&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;康城路-康城北路&lt;/div&gt;`)[0];\n", "                popup_56846bd0da2ccf74dde1928095e53151.setContent(html_9e5dc304a3f3f07fd8f324be6ac6c1a0);\n", "            \n", "        \n", "\n", "        marker_ece8f982f892ba598b17dda0ee1352cb.bindPopup(popup_56846bd0da2ccf74dde1928095e53151)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_ece8f982f892ba598b17dda0ee1352cb.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点12: 康城路-康城北路\n", "坐标: (29.5802331987334, 106.309370587699)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_e2aa467fd44e30d4935c898e329b5cb0 = L.marker(\n", "                [29.5730600920964, 106.303740502903],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_03a4a376d362cdab397897382cc2fee4 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_0fb6b2eb85be398e6d0e5ba85b5d4c22 = $(`&lt;div id=&quot;html_0fb6b2eb85be398e6d0e5ba85b5d4c22&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;康健路-曾家大道&lt;/div&gt;`)[0];\n", "                popup_03a4a376d362cdab397897382cc2fee4.setContent(html_0fb6b2eb85be398e6d0e5ba85b5d4c22);\n", "            \n", "        \n", "\n", "        marker_e2aa467fd44e30d4935c898e329b5cb0.bindPopup(popup_03a4a376d362cdab397897382cc2fee4)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_e2aa467fd44e30d4935c898e329b5cb0.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点13: 康健路-曾家大道\n", "坐标: (29.5730600920964, 106.303740502903)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_78e39b09ff7ccbb90cfcc1921fce5835 = L.marker(\n", "                [29.5683138615398, 106.303474210109],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_c5bdb08fbe7cb02e61c37e19d5c4a0bb = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_34b524245b9db293c02f1c4ed78fda4b = $(`&lt;div id=&quot;html_34b524245b9db293c02f1c4ed78fda4b&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;曾家大道-兴德路&lt;/div&gt;`)[0];\n", "                popup_c5bdb08fbe7cb02e61c37e19d5c4a0bb.setContent(html_34b524245b9db293c02f1c4ed78fda4b);\n", "            \n", "        \n", "\n", "        marker_78e39b09ff7ccbb90cfcc1921fce5835.bindPopup(popup_c5bdb08fbe7cb02e61c37e19d5c4a0bb)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_78e39b09ff7ccbb90cfcc1921fce5835.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点14: 曾家大道-兴德路\n", "坐标: (29.5683138615398, 106.303474210109)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_25e72c319e442b6b395cf44341f4c9c9 = L.marker(\n", "                [29.5638546264921, 106.299515068114],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_ed07c65ccadd536577562ef61ff7f213 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_8f728e873e357298fe4a7df5782fcab2 = $(`&lt;div id=&quot;html_8f728e873e357298fe4a7df5782fcab2&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;曾泰路-春阳路&lt;/div&gt;`)[0];\n", "                popup_ed07c65ccadd536577562ef61ff7f213.setContent(html_8f728e873e357298fe4a7df5782fcab2);\n", "            \n", "        \n", "\n", "        marker_25e72c319e442b6b395cf44341f4c9c9.bindPopup(popup_ed07c65ccadd536577562ef61ff7f213)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_25e72c319e442b6b395cf44341f4c9c9.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点15: 曾泰路-春阳路\n", "坐标: (29.5638546264921, 106.299515068114)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "    \n", "            var marker_2fd73cd3ed03487f3350a4b4bddbf040 = L.marker(\n", "                [29.5619637346162, 106.299288432515],\n", "                {\n", "}\n", "            ).addTo(map_ccf7f4c37f7ae78047bd2db7d26c8df0);\n", "        \n", "    \n", "        var popup_a36d7dc824fb04ccf9a38ee5bbcb6518 = L.popup({\n", "  &quot;maxWidth&quot;: &quot;100%&quot;,\n", "});\n", "\n", "        \n", "            \n", "                var html_939c66eae4cc6905f8a481f182b3b316 = $(`&lt;div id=&quot;html_939c66eae4cc6905f8a481f182b3b316&quot; style=&quot;width: 100.0%; height: 100.0%;&quot;&gt;春阳路-迎春路&lt;/div&gt;`)[0];\n", "                popup_a36d7dc824fb04ccf9a38ee5bbcb6518.setContent(html_939c66eae4cc6905f8a481f182b3b316);\n", "            \n", "        \n", "\n", "        marker_2fd73cd3ed03487f3350a4b4bddbf040.bindPopup(popup_a36d7dc824fb04ccf9a38ee5bbcb6518)\n", "        ;\n", "\n", "        \n", "    \n", "    \n", "            marker_2fd73cd3ed03487f3350a4b4bddbf040.bindTooltip(\n", "                `&lt;div&gt;\n", "                     点16: 春阳路-迎春路\n", "坐标: (29.5619637346162, 106.299288432515)\n", "                 &lt;/div&gt;`,\n", "                {\n", "  &quot;sticky&quot;: true,\n", "}\n", "            );\n", "        \n", "&lt;/script&gt;\n", "&lt;/html&gt;\" style=\"position:absolute;width:100%;height:100%;left:0;top:0;border:none !important;\" allowfullscreen webkitallowfullscreen mozallowfullscreen></iframe></div></div>"], "text/plain": ["<folium.folium.Map at 0x1b3fd17e930>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import folium\n", "import pandas as pd\n", "\n", "# 从Excel文件读取坐标数据\n", "excel_file = '停止线点坐标（下行）.xlsx'\n", "df = pd.read_excel(excel_file)\n", "\n", "# 提取坐标数据 (纬度, 经度)\n", "coordinates = list(zip(df['纬度'], df['经度']))\n", "stop_names = df['停止线名称'].tolist()\n", "\n", "print(f\"从Excel文件读取了 {len(coordinates)} 个坐标点\")\n", "print(\"前5个坐标点:\")\n", "for i in range(min(5, len(coordinates))):\n", "    print(f\"  {i+1}. {stop_names[i]}: ({coordinates[i][0]}, {coordinates[i][1]})\")\n", "\n", "# 计算中心点\n", "center_lat = sum(coord[0] for coord in coordinates) / len(coordinates)\n", "center_lon = sum(coord[1] for coord in coordinates) / len(coordinates)\n", "\n", "# 创建地图\n", "m = folium.Map(location=[center_lat, center_lon], zoom_start=13)\n", "\n", "# 添加标记点\n", "for i, ((lat, lon), name) in enumerate(zip(coordinates, stop_names), 1):\n", "    folium.Marker(\n", "        location=[lat, lon],\n", "        popup=f'{name}',\n", "        tooltip=f'点{i}: {name}\\n坐标: ({lat}, {lon})'\n", "    ).add_to(m)\n", "\n", "# 显示地图\n", "m"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}