import os
import csv
import math
import pandas as pd
from datetime import datetime
from collections import defaultdict

# 地球半径（米）
EARTH_RADIUS = 6371000


def haversine(lon1, lat1, lon2, lat2):
    """计算两个经纬度坐标之间的距离（米）"""
    lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    return EARTH_RADIUS * c


def find_nearest_stop_line(lon, lat, stop_lines, threshold=50):
    """找到最近的停止线（阈值50米内），返回停止线名称和距离"""
    min_distance = float('inf')
    nearest_stop_line = None
    
    for _, stop_line in stop_lines.iterrows():
        distance = haversine(lon, lat, stop_line['经度'], stop_line['纬度'])
        if distance <= threshold and distance < min_distance:
            min_distance = distance
            nearest_stop_line = stop_line['停止线名称']
    
    return nearest_stop_line, min_distance if nearest_stop_line else None


def safe_filename(name):
    """将交叉口名称转换为安全的文件夹名称"""
    # 替换不安全的字符
    unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    safe_name = name
    for char in unsafe_chars:
        safe_name = safe_name.replace(char, '_')
    return safe_name


def extract_intersection_data(trip_file, stop_lines, output_base_dir):
    """提取单个车辆文件中各个交叉口的数据"""
    print(f"正在处理文件: {trip_file}")
    
    # 读取轨迹数据
    data = []
    with open(trip_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)  # 保存标题行
        for row in reader:
            if len(row) < 5:
                continue
            try:
                gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                lon = float(row[2])
                lat = float(row[3])
                speed = float(row[4])
                data.append(row)  # 保存原始行数据
            except ValueError:
                continue

    if not data:
        print(f"  文件 {trip_file} 没有有效数据")
        return

    # 按交叉口分组数据
    intersection_data = defaultdict(list)
    
    for row in data:
        try:
            lon = float(row[2])
            lat = float(row[3])
            
            # 找到最近的停止线
            nearest_stop, distance = find_nearest_stop_line(lon, lat, stop_lines)
            if nearest_stop:
                intersection_data[nearest_stop].append(row)
        except (ValueError, IndexError):
            continue

    # 获取车辆编号
    car_num = os.path.basename(trip_file).replace('.txt', '')
    
    # 为每个交叉口保存数据
    for intersection, rows in intersection_data.items():
        if len(rows) > 0:  # 只保存有数据的交叉口
            # 创建交叉口文件夹
            safe_intersection_name = safe_filename(intersection)
            intersection_dir = os.path.join(output_base_dir, safe_intersection_name)
            os.makedirs(intersection_dir, exist_ok=True)
            
            # 保存该车在该交叉口的数据
            output_file = os.path.join(intersection_dir, f"{car_num}.txt")
            with open(output_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(header)  # 写入标题行
                writer.writerows(rows)   # 写入数据行
            
            print(f"  保存 {intersection}: {len(rows)} 条数据到 {output_file}")


def main():
    """主处理流程"""
    # 读取停止线数据（上行）
    stop_lines_file = '红绿灯停车分析/停止线点坐标（上行）.xlsx'
    stop_lines = pd.read_excel(stop_lines_file)
    stop_lines = stop_lines[stop_lines['方向'] == '上行']

    print(f"加载了 {len(stop_lines)} 个上行停止线坐标")
    print("停止线列表:")
    for _, stop_line in stop_lines.iterrows():
        print(f"  - {stop_line['停止线名称']}: ({stop_line['经度']}, {stop_line['纬度']})")

    # 获取所有下行数据文件
    downward_data_dir = '上行数据'
    trip_files = [f for f in os.listdir(downward_data_dir) if f.endswith('.txt')]
    
    print(f"\n找到 {len(trip_files)} 个上行数据文件")

    # 输出目录
    output_base_dir = '按交叉口分组的数据_上行'
    os.makedirs(output_base_dir, exist_ok=True)
    
    # 处理每个轨迹文件
    for i, trip_file in enumerate(trip_files, 1):
        print(f"\n处理文件 {i}/{len(trip_files)}: {trip_file}")

        trip_file_path = os.path.join(downward_data_dir, trip_file)
        extract_intersection_data(trip_file_path, stop_lines, output_base_dir)

    print(f"\n数据重组完成!")
    print(f"输出目录: {output_base_dir}")
    
    # 统计结果
    print("\n=== 统计结果 ===")
    if os.path.exists(output_base_dir):
        intersection_dirs = [d for d in os.listdir(output_base_dir) 
                           if os.path.isdir(os.path.join(output_base_dir, d))]
        
        print(f"共生成 {len(intersection_dirs)} 个交叉口文件夹:")
        
        for intersection_dir in sorted(intersection_dirs):
            intersection_path = os.path.join(output_base_dir, intersection_dir)
            txt_files = [f for f in os.listdir(intersection_path) if f.endswith('.txt')]
            
            # 统计总数据量
            total_records = 0
            for txt_file in txt_files:
                txt_path = os.path.join(intersection_path, txt_file)
                with open(txt_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    next(reader)  # 跳过标题行
                    total_records += sum(1 for _ in reader)
            
            print(f"  {intersection_dir}: {len(txt_files)} 个车辆文件, 共 {total_records} 条数据")

    print("\n现在您可以使用原有的停车分析逻辑对每个交叉口进行单独分析了！")


if __name__ == '__main__':
    main()
