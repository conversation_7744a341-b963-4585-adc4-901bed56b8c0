import os
import csv
import math
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from datetime import datetime

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 地球半径（米）
EARTH_RADIUS = 6371000


def haversine(lon1, lat1, lon2, lat2):
    """计算两个经纬度坐标之间的距离（米）"""
    lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    return EARTH_RADIUS * c


def is_near_stop_line(lon, lat, stop_lines, threshold=30):
    """检查点是否靠近任何停止线（阈值30米内）"""
    for _, stop_line in stop_lines.iterrows():
        distance = haversine(lon, lat, stop_line['经度'], stop_line['纬度'])
        if distance <= threshold:
            return True
    return False


def analyze_bus_trip(trip_file, stop_lines):
    """分析单次公交行程"""
    # 读取轨迹数据
    data = []
    with open(trip_file, 'r', encoding='utf-8') as f:  # 添加编码参数
        reader = csv.reader(f)
        next(reader)  # 跳过标题行
        for row in reader:
            if len(row) < 5:
                continue
            try:
                gps_time = datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S')
                lon = float(row[2])
                lat = float(row[3])
                speed = float(row[4])
                data.append((gps_time, lon, lat, speed))
            except ValueError:
                continue

    if not data:
        return None

    # 按时间排序
    data.sort(key=lambda x: x[0])

    # 计算总行程时间（秒）
    total_duration = (data[-1][0] - data[0][0]).total_seconds()

    # 分析停车事件
    stop_events = 0
    total_stop_time = 0
    current_stop_group = []
    in_stop_group = False

    for i, (time, lon, lat, speed) in enumerate(data):
        # 检查当前点是否在停止线附近且速度为0
        near_stop = is_near_stop_line(lon, lat, stop_lines)
        is_stopped = speed == 0.0

        # 如果满足停车条件
        if near_stop and is_stopped:
            if not in_stop_group:
                # 开始新的停车组
                in_stop_group = True
                current_stop_group = [time]
            else:
                # 继续当前停车组
                current_stop_group.append(time)
        else:
            if in_stop_group:
                # 结束当前停车组
                in_stop_group = False
                if len(current_stop_group) >= 2:
                    stop_events += 1
                    stop_time = (len(current_stop_group) - 1) * 10  # 每个间隔10秒
                    total_stop_time += stop_time
                current_stop_group = []

    # 处理最后可能存在的停车组
    if in_stop_group and len(current_stop_group) >= 2:
        stop_events += 1
        stop_time = (len(current_stop_group) - 1) * 10
        total_stop_time += stop_time

    # 计算停车时间比例
    stop_ratio = (total_stop_time / total_duration * 100) if total_duration > 0 else 0

    return {
        'file_name': os.path.basename(trip_file),
        'stop_events': stop_events,
        'total_stop_time': total_stop_time,
        'total_duration': total_duration,
        'stop_ratio': stop_ratio
    }


# 主处理流程
def main():
    # 读取停止线数据
    stop_lines = pd.read_excel('停止线点坐标.xlsx')
    stop_lines = stop_lines[stop_lines['方向'] == '下行']

    # 获取所有轨迹文件
    trip_files = [f for f in os.listdir('23178单趟行驶数据') if f.endswith('.txt')]
    results = []

    # 分析每个轨迹文件
    for trip_file in trip_files:
        result = analyze_bus_trip(os.path.join('23178单趟行驶数据', trip_file), stop_lines)
        if result:
            results.append(result)
            print(f"已分析文件: {trip_file}")  # 添加进度显示

    # 保存结果到CSV
    df_results = pd.DataFrame(results)
    df_results.to_csv('红绿灯等待情况分析.csv', index=False, encoding='utf_8_sig')  # 支持中文的编码

    print(f"分析完成! 共分析了 {len(results)} 趟公交车数据")
    print(f"结果已保存到: 红绿灯等待情况分析.csv")

    # 绘制图表
    plt.figure(figsize=(15, 12))  # 增加图表大小

    # 1. 被红灯截停次数的频次分布直方图
    plt.subplot(3, 1, 1)
    plt.hist(df_results['stop_events'], bins=np.arange(-0.5, df_results['stop_events'].max() + 1.5, 1),
             edgecolor='black', color='skyblue')
    plt.xlabel('被红灯截停次数', fontsize=12)
    plt.ylabel('频次', fontsize=12)
    plt.title('每趟车被红灯截停次数分布', fontsize=14, fontweight='bold')
    plt.grid(axis='y', alpha=0.5)

    # 添加平均值线
    mean_events = df_results['stop_events'].mean()
    plt.axvline(mean_events, color='red', linestyle='dashed', linewidth=1)
    plt.text(mean_events + 0.1, plt.ylim()[1] * 0.9, f'平均: {mean_events:.1f}', color='red')

    # 2. 等红灯时间的频率分布直方图
    plt.subplot(3, 1, 2)
    plt.hist(df_results['total_stop_time'], bins=20, edgecolor='black', color='lightgreen')
    plt.xlabel('等红灯时间（秒）', fontsize=12)
    plt.ylabel('频次', fontsize=12)
    plt.title('每趟车等红灯时间分布', fontsize=14, fontweight='bold')
    plt.grid(axis='y', alpha=0.5)

    # 添加平均值线
    mean_stop = df_results['total_stop_time'].mean()
    plt.axvline(mean_stop, color='red', linestyle='dashed', linewidth=1)
    plt.text(mean_stop + 5, plt.ylim()[1] * 0.9, f'平均: {mean_stop:.1f}秒', color='red')

    # 3. 等红灯时间占总行程时间比例的直方图
    plt.subplot(3, 1, 3)
    plt.hist(df_results['stop_ratio'], bins=20, edgecolor='black', color='salmon')
    plt.xlabel('等红灯时间占比（%）', fontsize=12)
    plt.ylabel('频次', fontsize=12)
    plt.title('等红灯时间占总行程时间比例分布', fontsize=14, fontweight='bold')
    plt.grid(axis='y', alpha=0.5)

    # 添加平均值线
    mean_ratio = df_results['stop_ratio'].mean()
    plt.axvline(mean_ratio, color='red', linestyle='dashed', linewidth=1)
    plt.text(mean_ratio + 0.5, plt.ylim()[1] * 0.9, f'平均: {mean_ratio:.1f}%', color='red')

    plt.tight_layout(pad=3.0)  # 增加子图间距
    plt.savefig('公交行程红绿灯分析.png', dpi=300, bbox_inches='tight')
    plt.show()


if __name__ == '__main__':
    main()
