#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版停止线坐标地图查看器
快速生成和查看坐标点地图
"""

import pandas as pd
import folium
import webbrowser
import os

def create_simple_map(excel_file='D:\\workfile\\公交数据\\公交集团轨迹和刷卡数据\\尝试性方法\\红绿灯停车分析\\停止线点坐标.xlsx'):
    """
    创建简单的坐标点地图
    
    Args:
        excel_file (str): Excel文件路径
    """
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file)
        print(f"读取到 {len(df)} 个坐标点")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 计算地图中心点
    center_lat = df['纬度'].mean()
    center_lon = df['经度'].mean()
    
    # 创建地图
    m = folium.Map(
        location=[center_lat, center_lon],
        zoom_start=13,
        tiles='OpenStreetMap'
    )
    
    # 添加标记点
    for idx, row in df.iterrows():
        folium.Marker(
            location=[row['纬度'], row['经度']],
            popup=f"<b>{row['停止线名称']}</b><br>方向: {row['方向']}<br>坐标: ({row['经度']:.6f}, {row['纬度']:.6f})",
            tooltip=row['停止线名称'],
            icon=folium.Icon(color='red' if row['方向'] == '下行' else 'blue', icon='info-sign')
        ).add_to(m)
    
    # 保存地图
    output_file = 'simple_map.html'
    m.save(output_file)
    print(f"地图已保存为: {output_file}")
    
    # 打开地图
    webbrowser.open(output_file)
    print("地图已在浏览器中打开")

if __name__ == "__main__":
    create_simple_map()
