import math
import os
from datetime import datetime


def distance(lon1, lat1, lon2, lat2):
    """计算两个坐标点之间的欧氏距离（近似）"""
    return math.sqrt((lon1 - lon2) ** 2 + (lat1 - lat2) ** 2)


def find_next_nonzero_speed(data, start_idx, step=1, count=3):
    """从指定索引开始查找第count个非零速度点"""
    found = 0
    idx = start_idx
    while 0 <= idx < len(data):
        if data[idx]['GpsSpeed'] > 0:
            found += 1
            if found == count:
                return idx
        idx += step
    return None  # 未找到足够点


def check_start_direction(data, idx):
    """检查是否满足下行起点方向条件"""
    # 查找起点下游第3个非零速度点
    target_idx = find_next_nonzero_speed(data, idx + 1, step=1, count=3)
    if target_idx is None:
        return False

    # 计算方向变化
    lon_diff = data[target_idx]['Longitude'] - data[idx]['Longitude']
    lat_diff = data[target_idx]['Latitude'] - data[idx]['Latitude']

    # 方向应该是经度增加（向东）且纬度减少（向南）
    return lon_diff > 0 and lat_diff < 0


def check_end_direction(data, idx):
    """检查是否满足下行终点方向条件"""
    # 查找终点上游第3个非零速度点
    target_idx = find_next_nonzero_speed(data, idx - 1, step=-1, count=3)
    if target_idx is None:
        return False

    # 计算方向变化
    lon_diff = data[idx]['Longitude'] - data[target_idx]['Longitude']
    lat_diff = data[idx]['Latitude'] - data[target_idx]['Latitude']

    # 方向应该是经度增加（向东）且纬度减少（向南）
    return lat_diff < 0   # and lon_diff > 0


def extract_down_trips(data):
    """从数据中提取下行行程"""
    # 定义关键坐标点
    start_ref_point = (106.339405, 29.617040)  # 起点参考点
    end_ref_point = (106.299286, 29.560120)  # 终点参考点

    all_down_trips = []  # 存储所有下行行程
    current_trip = []  # 当前正在处理的下行行程
    trip_count = {}  # 记录每个日期的行程计数
    recording = False  # 是否正在记录行程

    for idx, point in enumerate(data):
        lon, lat, speed = point['Longitude'], point['Latitude'], point['GpsSpeed']
        date_str = point['GpsTime'][:10]  # 提取日期部分

        # 第一步：检测下行起点
        if not recording:
            if speed == 0:
                dist = distance(lon, lat, *start_ref_point)
                if dist < 0.0002:  # 距离阈值设为0.0002度
                    # 使用改进的方向检测方法
                    if check_start_direction(data, idx):
                        recording = True
                        current_trip = [point]
                        car_num = point['CarNum']

        # 第二步：记录行程并检测终点
        else:
            current_trip.append(point)

            # 检查是否到达终点
            if speed == 0:
                dist = distance(lon, lat, *end_ref_point)
                if dist < 0.0005:  # 距离阈值设为0.0005度
                    # 使用改进的方向检测方法
                    if check_end_direction(data, idx):
                        recording = False

                        # 添加到总列表
                        all_down_trips.extend(current_trip)

                        # 更新行程计数
                        if date_str not in trip_count:
                            trip_count[date_str] = 1
                        else:
                            trip_count[date_str] += 1

                        # 生成文件名
                        trip_num = trip_count[date_str]
                        filename = f"{car_num}{date_str}下行第{trip_num}趟.txt"

                        # 保存当前行程
                        with open(filename, 'w') as f:
                            f.write("CarNum,GpsTime,Longitude,Latitude,GpsSpeed\n")
                            for p in current_trip:
                                f.write(
                                    f"{p['CarNum']},{p['GpsTime']},{p['Longitude']},{p['Latitude']},{p['GpsSpeed']}\n")

    return all_down_trips


def main():
    # 读取数据文件
    input_file = "CarNum_23178.txt"
    data = []
    with open(input_file, 'r') as f:
        headers = f.readline().strip().split(',')
        for line in f:
            parts = line.strip().split(',')
            if len(parts) != 5:
                continue

            data.append({
                'CarNum': parts[0],
                'GpsTime': parts[1],
                'Longitude': float(parts[2]),
                'Latitude': float(parts[3]),
                'GpsSpeed': float(parts[4])
            })

    # 提取下行行程
    all_down_data = extract_down_trips(data)

    # 第三步：保存汇总数据
    if all_down_data:
        # 获取日期范围
        start_date = all_down_data[0]['GpsTime'][:10]
        end_date = all_down_data[-1]['GpsTime'][:10]
        car_num = all_down_data[0]['CarNum']

        # 生成汇总文件名
        date_range = f"{start_date}-{end_date}" if start_date != end_date else start_date
        summary_file = f"{car_num}{date_range}下行数据汇总.txt"

        # 写入汇总文件
        with open(summary_file, 'w') as f:
            f.write("CarNum,GpsTime,Longitude,Latitude,GpsSpeed\n")
            for point in all_down_data:
                f.write(
                    f"{point['CarNum']},{point['GpsTime']},{point['Longitude']},{point['Latitude']},{point['GpsSpeed']}\n")

        print(f"处理完成！共提取{len(all_down_data)}条下行数据，已保存到{summary_file}")
    else:
        print("未找到任何下行行程数据")


if __name__ == "__main__":
    main()

