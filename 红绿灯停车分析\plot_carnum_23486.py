#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制下行数据文件夹中的CarNum_23486.txt，按行程分段显示
每一趟之间不连线，使用不同颜色区分
"""

import pandas as pd
import folium
import math

def calculate_distance(point1, point2):
    """
    计算两点间的距离（米）
    使用Haversine公式
    """
    lat1, lon1 = math.radians(point1[1]), math.radians(point1[0])
    lat2, lon2 = math.radians(point2[1]), math.radians(point2[0])

    dlat = lat2 - lat1
    dlon = lon2 - lon1

    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    r = 6371000  # 地球半径（米）

    return c * r

def is_near_terminal(point, start_point, end_point, threshold=60):
    """
    判断点是否接近起点或终点
    threshold: 距离阈值（米）
    """
    dist_to_start = calculate_distance(point, start_point)
    dist_to_end = calculate_distance(point, end_point)

    return dist_to_start <= threshold or dist_to_end <= threshold

def split_trips_by_time_gap(df, time_gap_minutes=30):
    """
    根据时间间隔分割行程
    当相邻两个GPS点的时间间隔超过指定分钟数时，认为是新的行程
    """
    trips = []
    current_trip = []

    for i, row in df.iterrows():
        if len(current_trip) == 0:
            # 开始新行程
            current_trip = [i]
        else:
            # 检查时间间隔
            prev_time = pd.to_datetime(df.iloc[current_trip[-1]]['GpsTime'])
            curr_time = pd.to_datetime(row['GpsTime'])
            time_diff = (curr_time - prev_time).total_seconds() / 60  # 转换为分钟

            if time_diff > time_gap_minutes:
                # 时间间隔过大，结束当前行程，开始新行程
                if len(current_trip) > 5:  # 只保留有足够数据点的行程
                    trips.append(df.iloc[current_trip].copy())
                current_trip = [i]
            else:
                # 继续当前行程
                current_trip.append(i)

    # 添加最后一个行程
    if len(current_trip) > 5:
        trips.append(df.iloc[current_trip].copy())

    return trips

def validate_trip_continuity(trip_data):
    """
    验证行程的连续性，相邻两点距离不能超过500米
    """
    for i in range(1, len(trip_data)):
        prev_point = (trip_data.iloc[i-1]['Longitude'], trip_data.iloc[i-1]['Latitude'])
        curr_point = (trip_data.iloc[i]['Longitude'], trip_data.iloc[i]['Latitude'])
        distance = calculate_distance(prev_point, curr_point)
        if distance > 500:
            return False
    return True

def create_trip_map(file_path, output_html):
    """
    创建分段行程地图
    """
    print(f"正在处理文件: {file_path}")

    # 读取数据
    df = pd.read_csv(file_path)

    # 过滤有效坐标
    df = df[(df['Longitude'] != 0.0) & (df['Latitude'] != 0.0)].copy()
    df['GpsTime'] = pd.to_datetime(df['GpsTime'])
    df = df.sort_values('GpsTime').reset_index(drop=True)

    if len(df) == 0:
        print("没有有效数据")
        return

    # 起点和终点坐标（下行方向）
    start_point = (106.339491, 29.617029)  # 下行起点
    end_point = (106.299375, 29.560237)    # 下行终点
    downward_checkpoint = (106.303990, 29.561718)  # 下行必经点

    # 分割行程（基于时间间隔）
    trips = split_trips_by_time_gap(df, time_gap_minutes=30)
    print(f"初步分割出 {len(trips)} 个行程")

    # 过滤和验证行程
    valid_trips = []
    for i, trip in enumerate(trips):
        # 验证连续性
        if not validate_trip_continuity(trip):
            print(f"  行程 {i+1}: 存在超过500米的跳跃，已排除")
            continue

        # 检查是否经过下行必经点
        passes_checkpoint = False
        for _, row in trip.iterrows():
            point = (row['Longitude'], row['Latitude'])
            if calculate_distance(point, downward_checkpoint) <= 50:
                passes_checkpoint = True
                break

        if passes_checkpoint:
            valid_trips.append(trip)
            print(f"  行程 {i+1}: 有效上行行程，{len(trip)} 个数据点")
        else:
            print(f"  行程 {i+1}: 未经过上行必经点，已排除")

    trips = valid_trips
    print(f"最终有效行程数: {len(trips)}")
    
    if len(trips) == 0:
        print("没有有效的上行行程数据")
        return

    # 计算地图中心点
    center_lat = df['Latitude'].mean()
    center_lon = df['Longitude'].mean()

    # 创建地图
    m = folium.Map(location=[center_lat, center_lon], zoom_start=12)

    # 添加起点和终点标记
    folium.Marker(
        [start_point[1], start_point[0]],
        popup="下行起点",
        icon=folium.Icon(color='green', icon='play')
    ).add_to(m)

    folium.Marker(
        [end_point[1], end_point[0]],
        popup="下行终点",
        icon=folium.Icon(color='red', icon='stop')
    ).add_to(m)

    # 添加下行必经点标记
    folium.Marker(
        [downward_checkpoint[1], downward_checkpoint[0]],
        popup="下行必经点",
        icon=folium.Icon(color='orange', icon='arrow-down')
    ).add_to(m)

    # 为每个行程生成不同颜色
    colors = ['blue', 'orange', 'purple', 'darkred', 'lightred', 'beige',
              'darkblue', 'darkgreen', 'cadetblue', 'darkpurple', 'white',
              'pink', 'lightblue', 'lightgreen', 'gray', 'black', 'yellow']
    
    # 绘制每个行程（每个行程独立，不连线）
    for i, trip in enumerate(trips):
        if len(trip) < 2:
            continue

        color = colors[i % len(colors)]

        # 创建轨迹坐标
        coords = [[row['Latitude'], row['Longitude']] for _, row in trip.iterrows()]

        # 添加轨迹线（每个行程独立）
        folium.PolyLine(
            coords,
            color=color,
            weight=3,
            opacity=0.8,
            popup=f"下行行程 {i+1}<br>数据点: {len(trip)}<br>开始时间: {trip.iloc[0]['GpsTime']}<br>结束时间: {trip.iloc[-1]['GpsTime']}"
        ).add_to(m)

        # 在行程起点添加标记
        start_coord = coords[0]
        folium.CircleMarker(
            start_coord,
            radius=6,
            popup=f"行程 {i+1} 起点<br>{trip.iloc[0]['GpsTime']}",
            color=color,
            fill=True,
            fillColor=color,
            weight=2
        ).add_to(m)

        # 在行程终点添加标记
        end_coord = coords[-1]
        folium.CircleMarker(
            end_coord,
            radius=4,
            popup=f"行程 {i+1} 终点<br>{trip.iloc[-1]['GpsTime']}",
            color=color,
            fill=True,
            fillColor='white',
            weight=2
        ).add_to(m)
    
    # 添加关键点的缓冲区
    folium.Circle(
        [start_point[1], start_point[0]],
        radius=60,
        popup="上行起点60米范围",
        color='green',
        fill=False,
        weight=2,
        opacity=0.5
    ).add_to(m)

    folium.Circle(
        [end_point[1], end_point[0]],
        radius=60,
        popup="上行终点60米范围",
        color='red',
        fill=False,
        weight=2,
        opacity=0.5
    ).add_to(m)

    folium.Circle(
        [downward_checkpoint[1], downward_checkpoint[0]],
        radius=50,
        popup="上行必经点50米范围",
        color='orange',
        fill=False,
        weight=2,
        opacity=0.5
    ).add_to(m)

    # 添加图例
    legend_html = '''
    <div style="position: fixed;
                bottom: 50px; left: 50px; width: 200px; height: 120px;
                background-color: white; border:2px solid grey; z-index:9999;
                font-size:12px; padding: 10px">
    <p><b>CarNum_23486 下行轨迹</b></p>
    <p><span style="color:green">●</span> 上行起点</p>
    <p><span style="color:red">●</span> 上行终点</p>
    <p><span style="color:orange">●</span> 上行必经点</p>
    <p>每条线代表一个独立行程</p>
    </div>
    '''
    m.get_root().html.add_child(folium.Element(legend_html))

    # 保存地图
    m.save(output_html)
    print(f"地图已保存: {output_html}")

    # 输出统计信息
    print(f"\n=== 行程统计信息 ===")
    total_points = 0
    for i, trip in enumerate(trips):
        start_time = trip.iloc[0]['GpsTime']
        end_time = trip.iloc[-1]['GpsTime']
        duration = (pd.to_datetime(end_time) - pd.to_datetime(start_time)).total_seconds() / 60
        total_points += len(trip)
        print(f"行程 {i+1}: {start_time} 到 {end_time}")
        print(f"        时长 {duration:.1f} 分钟, {len(trip)} 个数据点")

    print(f"\n总计: {len(trips)} 个上行行程, {total_points} 个GPS数据点")

def main():
    """
    主函数
    """
    file_path = "上行数据/CarNum_23685.txt"
    output_html = "上行分段轨迹_新版.html"

    print("=" * 50)
    print("CarNum_23685 上行数据可视化")
    print("=" * 50)

    create_trip_map(file_path, output_html)

    print(f"\n🎉 完成！请打开 {output_html} 查看分段轨迹图")
    print("\n📋 说明:")
    print("• 每个行程用不同颜色显示，行程之间不连线")
    print("• 绿色标记: 上行起点 (106.339491, 29.617029)")
    print("• 红色标记: 上行终点 (106.299375, 29.560237)")
    print("• 橙色标记: 上行必经点 (106.299398, 29.563687)")
    print("• 圆形标记: 实心为行程起点，空心为行程终点")
    print("• 只显示经过上行必经点的有效行程")

if __name__ == "__main__":
    main()
